<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unit 6b - Getting Around Reading App</title>
    <style>
        body {
            font-family: 'Comic Sans MS', cursive;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .header {
            background: #8e44ad;
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .section {
            margin-bottom: 30px;
            display: none;
        }
        
        .section.active {
            display: block;
        }
        
        .controls {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .text-container {
            background: #fff;
            border: 2px solid #ddd;
            border-radius: 10px;
            padding: 20px;
            font-size: 18px;
            line-height: 1.8;
            margin-bottom: 20px;
            min-height: 300px;
        }
        
        .highlight {
            background-color: #87CEEB;
            padding: 2px 4px;
            border-radius: 3px;
            transition: all 0.1s ease;
        }
        
        .question {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #8e44ad;
        }
        
        .options {
            margin-top: 15px;
        }
        
        .option {
            display: block;
            margin: 8px 0;
            padding: 10px;
            background: white;
            border: 2px solid #ddd;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .option:hover {
            border-color: #8e44ad;
            transform: translateX(5px);
        }
        
        .option.selected {
            background: #e8f4fd;
            border-color: #3498db;
        }
        
        .option.correct {
            background: #d4edda;
            border-color: #28a745;
        }
        
        .option.incorrect {
            background: #f8d7da;
            border-color: #dc3545;
        }
        
        .btn {
            background: #8e44ad;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: #732d91;
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .word-box {
            background: #e8f4fd;
            border: 2px dashed #3498db;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .word-bank {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
        }
        
        .word-item {
            background: #3498db;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .word-item:hover {
            background: #2980b9;
            transform: scale(1.05);
        }
        
        .gap {
            background: #fff3cd;
            border: 2px dashed #ffc107;
            padding: 5px 10px;
            margin: 0 2px;
            cursor: pointer;
            border-radius: 5px;
            display: inline-block;
            min-width: 60px;
            text-align: center;
        }
        
        .gap.revealed {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
            font-weight: bold;
        }
        
        .puzzle-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin: 20px 0;
        }
        
        .puzzle-level {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #ddd;
        }
        
        .drag-container {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin: 20px 0;
        }
        
        .sentence-parts {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            min-height: 400px;
        }
        
        .sentence-item {
            background: #3498db;
            color: white;
            padding: 15px;
            margin: 10px;
            border-radius: 8px;
            cursor: move;
            user-select: none;
            transition: all 0.3s ease;
        }
        
        .sentence-item:hover {
            background: #2980b9;
            transform: scale(1.02);
        }
        
        .sentence-item.dragging {
            opacity: 0.5;
        }
        
        .drop-zone {
            background: #fff;
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px;
            min-height: 60px;
            transition: all 0.3s ease;
        }
        
        .drop-zone.drag-over {
            border-color: #8e44ad;
            background: #f8f4fd;
        }
        
        .navigation {
            text-align: center;
            margin-top: 30px;
        }
        
        .progress {
            background: #e9ecef;
            border-radius: 10px;
            height: 20px;
            margin: 20px 0;
            overflow: hidden;
        }
        
        .progress-bar {
            background: linear-gradient(90deg, #8e44ad, #3498db);
            height: 100%;
            transition: width 0.3s ease;
        }
        
        .score-display {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            color: #8e44ad;
            margin: 20px 0;
        }
        
        input, select {
            padding: 8px 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        input:focus, select:focus {
            outline: none;
            border-color: #8e44ad;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚗 Unit 6b - Getting Around</h1>
            <p>Read about transportation tours and practice your skills!</p>
        </div>

        <!-- Speed Reading Section -->
        <div id="speed-reading" class="section active">
            <h2>📖 Speed Reading Practice</h2>
            <div class="controls">
                <label>Words per minute: <input type="number" id="wpm" value="200" min="50" max="800"></label>
                <label>Highlight: 
                    <select id="highlight-mode">
                        <option value="1">1 word</option>
                        <option value="2">2 words</option>
                        <option value="3">3 words</option>
                        <option value="sentence">Full sentence</option>
                    </select>
                </label>
                <button class="btn" onclick="startReading()">▶️ Start</button>
                <button class="btn" onclick="pauseReading()">⏸️ Pause</button>
                <button class="btn" onclick="resetReading()">🔄 Reset</button>
            </div>
            <div class="text-container" id="reading-text"></div>
            <div class="navigation">
                <button class="btn" onclick="showSection('comprehension')">Next: Comprehension Questions →</button>
            </div>
        </div>

        <!-- Comprehension Questions Section -->
        <div id="comprehension" class="section">
            <h2>🧠 Comprehension Questions</h2>
            <div id="questions-container"></div>
            <div class="navigation">
                <button class="btn" onclick="checkAnswers()">✅ Check Answers</button>
                <button class="btn" onclick="showAnswers()">👁️ Show Answers</button>
                <button class="btn" onclick="showSection('gap-fill')">Next: Gap Fill →</button>
            </div>
            <div class="score-display" id="comprehension-score"></div>
        </div>

        <!-- Gap Fill Section -->
        <div id="gap-fill" class="section">
            <h2>🔤 Fill in the Gaps</h2>
            <div class="word-box">
                <h3>Word Bank</h3>
                <div class="word-bank" id="word-bank"></div>
            </div>
            <div class="text-container" id="gap-text"></div>
            <div class="navigation">
                <button class="btn" onclick="revealAllGaps()">🔍 Reveal All Answers</button>
                <button class="btn" onclick="resetGapFill()">🔄 Reset Gap Fill</button>
                <button class="btn" onclick="showSection('capital-letters')">Next: Capital Letters →</button>
            </div>
        </div>

        <!-- Capital Letters Section -->
        <div id="capital-letters" class="section">
            <h2>🔤 Capital Letters Practice</h2>
            <div class="question">
                <h3>Theory: We use capital letters for:</h3>
                <ul>
                    <li>Starting a sentence, e.g. <em>Sunday</em></li>
                    <li>People's names, e.g. <em>Sally</em></li>
                    <li>Place names, e.g. <em>Manchester</em></li>
                    <li>Languages, e.g. <em>Spanish, German</em></li>
                    <li>Days & months, e.g. <em>Monday, July</em></li>
                    <li>Nationalities, e.g. <em>I'm British</em></li>
                </ul>
            </div>
            <div id="capital-questions-container"></div>
            <div class="navigation">
                <button class="btn" onclick="checkCapitalAnswers()">✅ Check Answers</button>
                <button class="btn" onclick="showCapitalAnswers()">👁️ Show Answers</button>
                <button class="btn" onclick="showSection('sentence-order')">Next: Sentence Order →</button>
            </div>
            <div class="score-display" id="capital-score"></div>
        </div>

        <!-- Sentence Ordering Section -->
        <div id="sentence-order" class="section">
            <h2>📝 Put Sentences in Order</h2>
            <div class="drag-container">
                <div class="sentence-parts">
                    <h3>Drag from here:</h3>
                    <div id="sentence-source"></div>
                </div>
                <div class="sentence-parts">
                    <h3>Drop in correct order:</h3>
                    <div id="sentence-target"></div>
                </div>
            </div>
            <div class="navigation">
                <button class="btn" onclick="checkOrder()">✅ Check Order</button>
                <button class="btn" onclick="showSection('interview')">Next: Interview Practice →</button>
            </div>
        </div>

        <!-- Interview Practice Section -->
        <div id="interview" class="section">
            <h2>🎤 Interview Practice</h2>
            <div class="question">
                <h3>Practice answering these interview questions about transportation:</h3>
                <p><em>Click on each question to see sample answers!</em></p>
            </div>
            <div id="interview-questions-container"></div>
            <div class="navigation">
                <button class="btn" onclick="showFinalResults()">🎉 Show Final Results</button>
            </div>
        </div>

        <!-- Final Results -->
        <div id="final-results" class="section">
            <h2>🎊 Congratulations!</h2>
            <div class="score-display" id="final-score-display"></div>
            <div class="navigation">
                <button class="btn" onclick="restartApp()">🔄 Start Over</button>
            </div>
        </div>

        <div class="progress">
            <div class="progress-bar" id="progress-bar" style="width: 14.28%"></div>
        </div>
    </div>

    <script>
        // Text content from Unit 6b - Getting Around (Transportation Tours)
        const fullText = `Are you bold enough to race across the snow on a snowmobile? Book one of our snowmobile tours - the chance to admire the beautiful Alaska scenery from a totally different point of view. Alaska is an experience of a lifetime.

See how people live in ancient China. Get in a rickshaw and go on a tour of the ancient city streets of Qian Chun. You can ask the driver to stop anywhere you like if you want to take a look around. Prices are not too high. The tour ranges from 30 to 50 yuan.

There is nothing as romantic as a Gondola Serenade Tour of Venice, the tour departing every evening towards Castello. Your gondolier will entertain you as if you travel up and down the waterways and past many famous landmarks. The tour lasts approximately one hour.

Transportation can be exciting and adventurous. Whether you choose a tram, gondola, car, bus, yacht, motorbike, bike, helicopter, plane, or hovercraft, each offers a unique way to explore the world.`;

        // Global variables
        let readingTimer;
        let currentWordIndex = 0;
        let words = [];
        let sentences = [];
        let isReading = false;
        let comprehensionAnswers = [];
        let userScores = {
            comprehension: 0,
            capital: 0,
            total: 0
        };

        // Initialize the app
        function initializeApp() {
            setupSpeedReading();
            setupComprehensionQuestions();
            setupGapFill();
            setupCapitalLetters();
            setupSentenceOrder();
            setupInterview();
        }

        // Speed Reading Functions
        function setupSpeedReading() {
            words = fullText.split(/\s+/);
            sentences = fullText.split(/[.!?]+/).filter(s => s.trim().length > 0);
            document.getElementById('reading-text').innerHTML = fullText;
        }

        function startReading() {
            if (isReading) return;
            
            isReading = true;
            const wpm = parseInt(document.getElementById('wpm').value);
            const highlightMode = document.getElementById('highlight-mode').value;
            const interval = 60000 / wpm; // milliseconds per word
            
            readingTimer = setInterval(() => {
                highlightWords(highlightMode);
            }, interval);
        }

        function highlightWords(mode) {
            const textContainer = document.getElementById('reading-text');
            let wordsToHighlight = 1;
            
            if (mode === 'sentence') {
                // Find current sentence
                let wordCount = 0;
                for (let i = 0; i < sentences.length; i++) {
                    const sentenceWords = sentences[i].trim().split(/\s+/).length;
                    if (currentWordIndex < wordCount + sentenceWords) {
                        highlightSentence(i);
                        currentWordIndex = wordCount + sentenceWords;
                        break;
                    }
                    wordCount += sentenceWords;
                }
            } else {
                wordsToHighlight = parseInt(mode);
                highlightWordGroup(wordsToHighlight);
            }
            
            if (currentWordIndex >= words.length) {
                pauseReading();
            }
        }

        function highlightWordGroup(count) {
            const textContainer = document.getElementById('reading-text');
            let html = '';
            let wordIndex = 0;
            
            const wordsArray = fullText.split(/(\s+)/);
            
            for (let i = 0; i < wordsArray.length; i++) {
                if (wordsArray[i].trim().length > 0) {
                    if (wordIndex >= currentWordIndex && wordIndex < currentWordIndex + count) {
                        html += `<span class="highlight">${wordsArray[i]}</span>`;
                    } else {
                        html += wordsArray[i];
                    }
                    wordIndex++;
                } else {
                    html += wordsArray[i];
                }
            }
            
            textContainer.innerHTML = html;
            currentWordIndex += count;
        }

        function highlightSentence(sentenceIndex) {
            const textContainer = document.getElementById('reading-text');
            let html = fullText;
            
            if (sentences[sentenceIndex]) {
                const sentence = sentences[sentenceIndex].trim();
                html = html.replace(sentence, `<span class="highlight">${sentence}</span>`);
            }
            
            textContainer.innerHTML = html;
        }

        function pauseReading() {
            isReading = false;
            if (readingTimer) {
                clearInterval(readingTimer);
            }
        }

        function resetReading() {
            pauseReading();
            currentWordIndex = 0;
            document.getElementById('reading-text').innerHTML = fullText;
        }

        // Comprehension Questions
        function setupComprehensionQuestions() {
            const questions = [
                {
                    question: "What transportation is used for the Alaska tour?",
                    options: ["Helicopter", "Snowmobile", "Car", "Plane"],
                    correct: 1
                },
                {
                    question: "What can you see on the Alaska tour?",
                    options: ["City streets", "Ancient buildings", "Beautiful scenery", "Famous landmarks"],
                    correct: 2
                },
                {
                    question: "What transportation is used in ancient China?",
                    options: ["Gondola", "Rickshaw", "Snowmobile", "Yacht"],
                    correct: 1
                },
                {
                    question: "Where does the rickshaw tour take place?",
                    options: ["Alaska", "Venice", "Qian Chun", "Castello"],
                    correct: 2
                },
                {
                    question: "How much does the rickshaw tour cost?",
                    options: ["20-40 yuan", "30-50 yuan", "40-60 yuan", "50-70 yuan"],
                    correct: 1
                },
                {
                    question: "What transportation is used in Venice?",
                    options: ["Rickshaw", "Snowmobile", "Gondola", "Hovercraft"],
                    correct: 2
                },
                {
                    question: "When do the Venice tours depart?",
                    options: ["Every morning", "Every afternoon", "Every evening", "Every night"],
                    correct: 2
                },
                {
                    question: "How long does the Venice tour last?",
                    options: ["30 minutes", "One hour", "Two hours", "Three hours"],
                    correct: 1
                },
                {
                    question: "What does the gondolier do during the tour?",
                    options: ["Takes photos", "Serves food", "Entertains you", "Sells souvenirs"],
                    correct: 2
                },
                {
                    question: "Which word best describes the gondola tour?",
                    options: ["Adventurous", "Romantic", "Dangerous", "Expensive"],
                    correct: 1
                }
            ];

            const container = document.getElementById('questions-container');
            questions.forEach((q, index) => {
                const questionDiv = document.createElement('div');
                questionDiv.className = 'question';
                questionDiv.innerHTML = `
                    <h3>Question ${index + 1}</h3>
                    <p><strong>${q.question}</strong></p>
                    <div class="options">
                        ${q.options.map((option, optIndex) => `
                            <label class="option" onclick="selectOption(${index}, ${optIndex})">
                                <input type="radio" name="q${index}" value="${optIndex}" style="margin-right: 10px;">
                                ${String.fromCharCode(97 + optIndex)}) ${option}
                            </label>
                        `).join('')}
                    </div>
                `;
                container.appendChild(questionDiv);
            });

            comprehensionAnswers = questions.map(q => q.correct);
        }

        function selectOption(questionIndex, optionIndex) {
            const options = document.querySelectorAll(`input[name="q${questionIndex}"]`);
            options.forEach(opt => opt.checked = false);
            options[optionIndex].checked = true;
            
            const labels = document.querySelectorAll(`input[name="q${questionIndex}"]`).forEach((input, idx) => {
                input.parentElement.classList.remove('selected');
            });
            options[optionIndex].parentElement.classList.add('selected');
        }

        function checkAnswers() {
            let correct = 0;
            for (let i = 0; i < comprehensionAnswers.length; i++) {
                const selected = document.querySelector(`input[name="q${i}"]:checked`);
                if (selected && parseInt(selected.value) === comprehensionAnswers[i]) {
                    correct++;
                    selected.parentElement.classList.add('correct');
                } else if (selected) {
                    selected.parentElement.classList.add('incorrect');
                }
            }
            
            userScores.comprehension = Math.round((correct / comprehensionAnswers.length) * 100);
            document.getElementById('comprehension-score').textContent = 
                `Score: ${correct}/${comprehensionAnswers.length} (${userScores.comprehension}%)`;
        }

        function showAnswers() {
            for (let i = 0; i < comprehensionAnswers.length; i++) {
                const correctOption = document.querySelector(`input[name="q${i}"][value="${comprehensionAnswers[i]}"]`);
                if (correctOption) {
                    correctOption.parentElement.classList.add('correct');
                }
            }
        }

        // Gap Fill Functions
        function setupGapFill() {
            // Define words in the order they appear in the text
            const gapWordsInOrder = [
                { word: 'bold', position: 'Are you _____ enough' },
                { word: 'snowmobile', position: 'race across the snow on a _____' },
                { word: 'Alaska', position: 'beautiful _____ scenery' },
                { word: 'experience', position: '_____ of a lifetime' },
                { word: 'rickshaw', position: 'Get in a _____' },
                { word: 'ancient', position: 'tour of the _____ city streets' },
                { word: 'driver', position: 'ask the _____ to stop' },
                { word: 'yuan', position: 'ranges from 30 to 50 _____' },
                { word: 'romantic', position: 'nothing as _____ as a Gondola' },
                { word: 'evening', position: 'departing every _____' },
                { word: 'gondolier', position: 'Your _____ will entertain you' },
                { word: 'landmarks', position: 'many famous _____' }
            ];

            const wordBank = document.getElementById('word-bank');

            // Shuffle the words for the word bank
            const shuffledWords = [...gapWordsInOrder].sort(() => Math.random() - 0.5);
            shuffledWords.forEach(item => {
                const wordElement = document.createElement('span');
                wordElement.className = 'word-item';
                wordElement.textContent = item.word;
                wordBank.appendChild(wordElement);
            });

            // Create gap text by replacing words in order they appear
            let gapText = fullText;
            gapWordsInOrder.forEach((item, index) => {
                // Use a more specific regex to match the exact word
                const regex = new RegExp(`\\b${item.word}\\b`, 'i');
                gapText = gapText.replace(regex,
                    `<span class="gap" onclick="revealGap(${index})" data-answer="${item.word}">${index + 1}</span>`);
            });

            document.getElementById('gap-text').innerHTML = gapText;
            window.gapAnswers = gapWordsInOrder.map(item => item.word);
        }

        function revealGap(index) {
            // Find the specific gap by its data-answer attribute
            const targetGap = document.querySelector(`.gap[onclick="revealGap(${index})"]`);
            if (targetGap && !targetGap.classList.contains('revealed')) {
                targetGap.classList.add('revealed');
                targetGap.textContent = window.gapAnswers[index];
                targetGap.onclick = null; // Remove click handler after revealing
            }
        }

        function revealAllGaps() {
            for (let i = 0; i < window.gapAnswers.length; i++) {
                revealGap(i);
            }
        }

        function resetGapFill() {
            // Reset the gap fill section
            setupGapFill();
        }

        // Capital Letters Functions
        function setupCapitalLetters() {
            const capitalQuestions = [
                {
                    question: "tourists visit alaska for snowmobile tours.",
                    correct: "Tourists visit Alaska for snowmobile tours.",
                    options: ["tourists visit alaska for snowmobile tours.", "Tourists visit alaska for snowmobile tours.", "Tourists visit Alaska for snowmobile tours.", "tourists Visit Alaska For Snowmobile Tours."]
                },
                {
                    question: "people in china use rickshaws for transportation.",
                    correct: "People in China use rickshaws for transportation.",
                    options: ["people in china use rickshaws for transportation.", "People in china use rickshaws for transportation.", "People in China use rickshaws for transportation.", "people In China Use Rickshaws For Transportation."]
                },
                {
                    question: "venice is famous for its gondola tours.",
                    correct: "Venice is famous for its gondola tours.",
                    options: ["venice is famous for its gondola tours.", "Venice is famous for its gondola tours.", "Venice Is Famous For Its Gondola Tours.", "venice Is Famous For Its Gondola Tours."]
                },
                {
                    question: "i want to visit qian chun next summer.",
                    correct: "I want to visit Qian Chun next summer.",
                    options: ["i want to visit qian chun next summer.", "I want to visit qian chun next summer.", "I want to visit Qian Chun next summer.", "i Want To Visit Qian Chun Next Summer."]
                },
                {
                    question: "castello is a beautiful area in italy.",
                    correct: "Castello is a beautiful area in Italy.",
                    options: ["castello is a beautiful area in italy.", "Castello is a beautiful area in italy.", "Castello is a beautiful area in Italy.", "castello Is A Beautiful Area In Italy."]
                }
            ];

            const container = document.getElementById('capital-questions-container');
            capitalQuestions.forEach((q, index) => {
                const questionDiv = document.createElement('div');
                questionDiv.className = 'question';
                questionDiv.innerHTML = `
                    <h3>Question ${index + 1}</h3>
                    <p><strong>Add capital letters to: "${q.question}"</strong></p>
                    <div class="options">
                        ${q.options.map((option, optIndex) => `
                            <label class="option" onclick="selectCapitalOption(${index}, ${optIndex})">
                                <input type="radio" name="capital${index}" value="${optIndex}" style="margin-right: 10px;">
                                ${String.fromCharCode(97 + optIndex)}) ${option}
                            </label>
                        `).join('')}
                    </div>
                `;
                container.appendChild(questionDiv);
            });

            window.capitalAnswers = capitalQuestions.map(q => q.options.indexOf(q.correct));
        }

        function selectCapitalOption(questionIndex, optionIndex) {
            const options = document.querySelectorAll(`input[name="capital${questionIndex}"]`);
            options.forEach(opt => {
                opt.checked = false;
                opt.parentElement.classList.remove('selected');
            });
            options[optionIndex].checked = true;
            options[optionIndex].parentElement.classList.add('selected');
        }

        function checkCapitalAnswers() {
            let correct = 0;
            for (let i = 0; i < window.capitalAnswers.length; i++) {
                const selected = document.querySelector(`input[name="capital${i}"]:checked`);
                if (selected && parseInt(selected.value) === window.capitalAnswers[i]) {
                    correct++;
                    selected.parentElement.classList.add('correct');
                } else if (selected) {
                    selected.parentElement.classList.add('incorrect');
                }
            }

            userScores.capital = Math.round((correct / window.capitalAnswers.length) * 100);
            document.getElementById('capital-score').textContent =
                `Score: ${correct}/${window.capitalAnswers.length} (${userScores.capital}%)`;
        }

        function showCapitalAnswers() {
            for (let i = 0; i < window.capitalAnswers.length; i++) {
                const correctOption = document.querySelector(`input[name="capital${i}"][value="${window.capitalAnswers[i]}"]`);
                if (correctOption) {
                    correctOption.parentElement.classList.add('correct');
                }
            }
        }

        // Sentence Ordering Functions
        function setupSentenceOrder() {
            const sentenceParts = [
                "Are you bold enough to race across the snow on a snowmobile?",
                "Book one of our snowmobile tours in Alaska.",
                "Get in a rickshaw and go on a tour of ancient city streets.",
                "You can ask the driver to stop anywhere you like.",
                "There is nothing as romantic as a Gondola Serenade Tour of Venice.",
                "The tour departs every evening towards Castello.",
                "Your gondolier will entertain you as you travel.",
                "The tour lasts approximately one hour."
            ];

            const shuffled = [...sentenceParts].sort(() => Math.random() - 0.5);
            const sourceContainer = document.getElementById('sentence-source');
            const targetContainer = document.getElementById('sentence-target');

            shuffled.forEach((sentence, index) => {
                const sentenceElement = document.createElement('div');
                sentenceElement.className = 'sentence-item';
                sentenceElement.textContent = sentence;
                sentenceElement.draggable = true;
                sentenceElement.dataset.original = sentenceParts.indexOf(sentence);
                
                sentenceElement.addEventListener('dragstart', handleDragStart);
                sentenceElement.addEventListener('dragend', handleDragEnd);
                
                sourceContainer.appendChild(sentenceElement);
            });

            // Create drop zones
            for (let i = 0; i < 8; i++) {
                const dropZone = document.createElement('div');
                dropZone.className = 'drop-zone';
                dropZone.textContent = `Position ${i + 1}`;
                dropZone.dataset.position = i;
                
                dropZone.addEventListener('dragover', handleDragOver);
                dropZone.addEventListener('drop', handleDrop);
                dropZone.addEventListener('dragleave', handleDragLeave);
                
                targetContainer.appendChild(dropZone);
            }

            window.correctOrder = sentenceParts;
        }

        function handleDragStart(e) {
            e.target.classList.add('dragging');
            e.dataTransfer.setData('text/plain', e.target.textContent);
            e.dataTransfer.setData('application/json', JSON.stringify({
                text: e.target.textContent,
                original: e.target.dataset.original
            }));
        }

        function handleDragEnd(e) {
            e.target.classList.remove('dragging');
        }

        function handleDragOver(e) {
            e.preventDefault();
            e.target.classList.add('drag-over');
        }

        function handleDragLeave(e) {
            e.target.classList.remove('drag-over');
        }

        function handleDrop(e) {
            e.preventDefault();
            e.target.classList.remove('drag-over');
            
            const data = JSON.parse(e.dataTransfer.getData('application/json'));
            e.target.textContent = data.text;
            e.target.dataset.original = data.original;
            e.target.style.background = '#e8f4fd';
        }

        function checkOrder() {
            const dropZones = document.querySelectorAll('.drop-zone');
            let correct = 0;
            
            dropZones.forEach((zone, index) => {
                if (zone.dataset.original == index) {
                    zone.style.background = '#d4edda';
                    correct++;
                } else {
                    zone.style.background = '#f8d7da';
                }
            });
            
            alert(`You got ${correct}/8 sentences in the correct order!`);
        }

        // Interview Practice Functions
        function setupInterview() {
            const interviewQuestions = [
                {
                    question: "What is your favorite way to travel?",
                    sampleAnswers: [
                        "I love traveling by plane because it's fast and exciting.",
                        "I prefer trains because you can see the scenery and relax.",
                        "I enjoy cycling because it's good exercise and environmentally friendly."
                    ]
                },
                {
                    question: "Have you ever been on an unusual transportation tour?",
                    sampleAnswers: [
                        "Yes, I went on a gondola tour in Venice and it was very romantic.",
                        "I tried a rickshaw tour in Asia and it was a unique experience.",
                        "No, but I would love to try a snowmobile tour in Alaska."
                    ]
                },
                {
                    question: "Which transportation tour would you most like to try?",
                    sampleAnswers: [
                        "I'd love to try the snowmobile tour in Alaska because it sounds adventurous.",
                        "The gondola tour in Venice appeals to me because it's romantic.",
                        "I'm interested in the rickshaw tour because I want to see ancient culture."
                    ]
                }
            ];

            const container = document.getElementById('interview-questions-container');
            interviewQuestions.forEach((q, index) => {
                const questionDiv = document.createElement('div');
                questionDiv.className = 'question';
                questionDiv.innerHTML = `
                    <h3>Question ${index + 1}</h3>
                    <p><strong>${q.question}</strong></p>
                    <button class="btn" onclick="toggleAnswers(${index})" style="margin: 10px 0;">Show Sample Answers</button>
                    <div id="answers-${index}" style="display: none; margin-top: 15px;">
                        <h4>Sample Answers:</h4>
                        <ul>
                            ${q.sampleAnswers.map(answer => `<li style="margin: 8px 0; padding: 8px; background: #f8f9fa; border-radius: 5px;">${answer}</li>`).join('')}
                        </ul>
                        <p style="margin-top: 15px; font-style: italic; color: #666;">Now try to answer this question yourself!</p>
                    </div>
                `;
                container.appendChild(questionDiv);
            });
        }

        function toggleAnswers(questionIndex) {
            const answersDiv = document.getElementById(`answers-${questionIndex}`);
            const button = event.target;

            if (answersDiv.style.display === 'none') {
                answersDiv.style.display = 'block';
                button.textContent = 'Hide Sample Answers';
            } else {
                answersDiv.style.display = 'none';
                button.textContent = 'Show Sample Answers';
            }
        }



        // Navigation and Progress Functions
        function showSection(sectionId) {
            // Hide all sections
            document.querySelectorAll('.section').forEach(section => {
                section.classList.remove('active');
            });
            
            // Show selected section
            document.getElementById(sectionId).classList.add('active');
            
            // Update progress bar
            const sections = ['speed-reading', 'comprehension', 'gap-fill', 'capital-letters', 'sentence-order', 'interview', 'final-results'];
            const currentIndex = sections.indexOf(sectionId);
            const progress = ((currentIndex + 1) / sections.length) * 100;
            document.getElementById('progress-bar').style.width = progress + '%';
        }

        function showFinalResults() {
            userScores.total = Math.round((userScores.comprehension + userScores.capital) / 2);

            let message = '';
            if (userScores.total >= 90) {
                message = '🌟 Excellent! You are a reading superstar!';
            } else if (userScores.total >= 80) {
                message = '🎉 Great job! You have strong reading skills!';
            } else if (userScores.total >= 70) {
                message = '👍 Good work! Keep practicing to improve!';
            } else {
                message = '📚 Keep reading and practicing! You can do it!';
            }

            document.getElementById('final-score-display').innerHTML = `
                <h3>${message}</h3>
                <p>Comprehension Score: ${userScores.comprehension}%</p>
                <p>Capital Letters Score: ${userScores.capital}%</p>
                <p><strong>Overall Score: ${userScores.total}%</strong></p>
                <p style="margin-top: 20px; font-style: italic;">Great job completing the Unit 6b - Getting Around activities! You've practiced reading comprehension, transportation vocabulary, and grammar skills.</p>
            `;

            showSection('final-results');
        }

        function restartApp() {
            // Reset all scores
            userScores = { comprehension: 0, capital: 0, total: 0 };
            
            // Reset reading
            resetReading();
            
            // Clear all selections
            document.querySelectorAll('input[type="radio"]').forEach(input => {
                input.checked = false;
                input.parentElement.classList.remove('selected', 'correct', 'incorrect');
            });
            
            // Reset gap fill
            document.querySelectorAll('.gap').forEach((gap, index) => {
                gap.classList.remove('revealed');
                const gapNumber = gap.getAttribute('onclick')?.match(/revealGap\((\d+)\)/)?.[1];
                if (gapNumber) {
                    gap.textContent = parseInt(gapNumber) + 1;
                    gap.onclick = () => revealGap(parseInt(gapNumber));
                }
            });
            
            // Reset sentence order
            const sourceContainer = document.getElementById('sentence-source');
            const targetContainer = document.getElementById('sentence-target');
            const dropZones = document.querySelectorAll('.drop-zone');
            
            dropZones.forEach((zone, index) => {
                if (zone.dataset.original !== undefined) {
                    const sentenceElement = document.createElement('div');
                    sentenceElement.className = 'sentence-item';
                    sentenceElement.textContent = zone.textContent;
                    sentenceElement.draggable = true;
                    sentenceElement.dataset.original = zone.dataset.original;
                    
                    sentenceElement.addEventListener('dragstart', handleDragStart);
                    sentenceElement.addEventListener('dragend', handleDragEnd);
                    
                    sourceContainer.appendChild(sentenceElement);
                }
                
                zone.textContent = `Position ${index + 1}`;
                zone.style.background = '';
                delete zone.dataset.original;
            });
            
            // Clear scores
            document.getElementById('comprehension-score').textContent = '';
            document.getElementById('capital-score').textContent = '';
            
            // Go back to first section
            showSection('speed-reading');
        }

        // Initialize the app when page loads
        window.onload = function() {
            initializeApp();
        };
    </script>
</body>
</html>