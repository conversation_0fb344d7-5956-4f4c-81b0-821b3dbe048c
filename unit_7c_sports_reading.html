<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unit 7c - Any Ideas? Sports Reading App</title>
    <style>
        body {
            font-family: 'Comic Sans MS', cursive;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .header {
            background: #8e44ad;
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .section {
            margin-bottom: 30px;
            display: none;
        }
        
        .section.active {
            display: block;
        }
        
        .controls {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .text-container {
            background: #fff;
            border: 2px solid #ddd;
            border-radius: 10px;
            padding: 20px;
            font-size: 18px;
            line-height: 1.8;
            margin-bottom: 20px;
            min-height: 300px;
        }
        
        .highlight {
            background-color: #87CEEB;
            padding: 2px 4px;
            border-radius: 3px;
            transition: all 0.1s ease;
        }
        
        .question {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #8e44ad;
        }
        
        .options {
            margin-top: 15px;
        }
        
        .option {
            display: block;
            margin: 8px 0;
            padding: 10px;
            background: white;
            border: 2px solid #ddd;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .option:hover {
            border-color: #8e44ad;
            transform: translateX(5px);
        }
        
        .option.selected {
            background: #e8f4fd;
            border-color: #3498db;
        }
        
        .option.correct {
            background: #d4edda;
            border-color: #28a745;
        }
        
        .option.incorrect {
            background: #f8d7da;
            border-color: #dc3545;
        }
        
        .btn {
            background: #8e44ad;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: #732d91;
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .word-box {
            background: #e8f4fd;
            border: 2px dashed #3498db;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .word-bank {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
        }
        
        .word-item {
            background: #3498db;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .word-item:hover {
            background: #2980b9;
            transform: scale(1.05);
        }
        
        .gap {
            background: #fff3cd;
            border: 2px dashed #ffc107;
            padding: 5px 10px;
            margin: 0 2px;
            cursor: pointer;
            border-radius: 5px;
            display: inline-block;
            min-width: 60px;
            text-align: center;
        }
        
        .gap.revealed {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
            font-weight: bold;
        }
        
        .puzzle-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin: 20px 0;
        }
        
        .puzzle-level {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #ddd;
        }
        
        .drag-container {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin: 20px 0;
        }
        
        .sentence-parts {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            min-height: 400px;
        }
        
        .sentence-item {
            background: #3498db;
            color: white;
            padding: 15px;
            margin: 10px;
            border-radius: 8px;
            cursor: move;
            user-select: none;
            transition: all 0.3s ease;
        }
        
        .sentence-item:hover {
            background: #2980b9;
            transform: scale(1.02);
        }
        
        .sentence-item.dragging {
            opacity: 0.5;
        }
        
        .drop-zone {
            background: #fff;
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px;
            min-height: 60px;
            transition: all 0.3s ease;
        }
        
        .drop-zone.drag-over {
            border-color: #8e44ad;
            background: #f8f4fd;
        }
        
        .navigation {
            text-align: center;
            margin-top: 30px;
        }
        
        .progress {
            background: #e9ecef;
            border-radius: 10px;
            height: 20px;
            margin: 20px 0;
            overflow: hidden;
        }
        
        .progress-bar {
            background: linear-gradient(90deg, #8e44ad, #3498db);
            height: 100%;
            transition: width 0.3s ease;
        }
        
        .score-display {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            color: #8e44ad;
            margin: 20px 0;
        }
        
        input, select {
            padding: 8px 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        input:focus, select:focus {
            outline: none;
            border-color: #8e44ad;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚽ Unit 7c - Any Ideas?</h1>
            <p>Read about sports and activities and practice your skills!</p>
        </div>

        <!-- Speed Reading Section -->
        <div id="speed-reading" class="section active">
            <h2>📖 Speed Reading Practice</h2>
            <div class="controls">
                <label>Words per minute: <input type="number" id="wpm" value="200" min="50" max="800"></label>
                <label>Highlight: 
                    <select id="highlight-mode">
                        <option value="1">1 word</option>
                        <option value="2">2 words</option>
                        <option value="3">3 words</option>
                        <option value="sentence">Full sentence</option>
                    </select>
                </label>
                <button class="btn" onclick="startReading()">▶️ Start</button>
                <button class="btn" onclick="pauseReading()">⏸️ Pause</button>
                <button class="btn" onclick="resetReading()">🔄 Reset</button>
            </div>
            <div class="text-container" id="reading-text"></div>
            <div class="navigation">
                <button class="btn" onclick="showSection('comprehension')">Next: Comprehension Questions →</button>
            </div>
        </div>

        <!-- Comprehension Questions Section -->
        <div id="comprehension" class="section">
            <h2>🧠 Comprehension Questions</h2>
            <div id="questions-container"></div>
            <div class="navigation">
                <button class="btn" onclick="checkAnswers()">✅ Check Answers</button>
                <button class="btn" onclick="showAnswers()">👁️ Show Answers</button>
                <button class="btn" onclick="showSection('gap-fill')">Next: Gap Fill →</button>
            </div>
            <div class="score-display" id="comprehension-score"></div>
        </div>

        <!-- Gap Fill Section -->
        <div id="gap-fill" class="section">
            <h2>🔤 Fill in the Gaps</h2>
            <div class="word-box">
                <h3>Word Bank</h3>
                <div class="word-bank" id="word-bank"></div>
            </div>
            <div class="text-container" id="gap-text"></div>
            <div class="navigation">
                <button class="btn" onclick="revealAllGaps()">🔍 Reveal All Answers</button>
                <button class="btn" onclick="resetGapFill()">🔄 Reset Gap Fill</button>
                <button class="btn" onclick="showSection('capital-letters')">Next: Capital Letters →</button>
            </div>
        </div>

        <!-- Capital Letters Section -->
        <div id="capital-letters" class="section">
            <h2>🔤 Capital Letters Practice</h2>
            <div class="question">
                <h3>Theory: We use capital letters for:</h3>
                <ul>
                    <li>Starting a sentence, e.g. <em>Sunday</em></li>
                    <li>People's names, e.g. <em>Sally</em></li>
                    <li>Place names, e.g. <em>Manchester</em></li>
                    <li>Languages, e.g. <em>Spanish, German</em></li>
                    <li>Days & months, e.g. <em>Monday, July</em></li>
                    <li>Nationalities, e.g. <em>I'm British</em></li>
                </ul>
            </div>
            <div id="capital-questions-container"></div>
            <div class="navigation">
                <button class="btn" onclick="checkCapitalAnswers()">✅ Check Answers</button>
                <button class="btn" onclick="showCapitalAnswers()">👁️ Show Answers</button>
                <button class="btn" onclick="showSection('sentence-order')">Next: Sentence Order →</button>
            </div>
            <div class="score-display" id="capital-score"></div>
        </div>

        <!-- Sentence Ordering Section -->
        <div id="sentence-order" class="section">
            <h2>📝 Put Sentences in Order</h2>
            <div class="drag-container">
                <div class="sentence-parts">
                    <h3>Drag from here:</h3>
                    <div id="sentence-source"></div>
                </div>
                <div class="sentence-parts">
                    <h3>Drop in correct order:</h3>
                    <div id="sentence-target"></div>
                </div>
            </div>
            <div class="navigation">
                <button class="btn" onclick="checkOrder()">✅ Check Order</button>
                <button class="btn" onclick="showSection('interview')">Next: Interview Practice →</button>
            </div>
        </div>

        <!-- Interview Practice Section -->
        <div id="interview" class="section">
            <h2>🎤 Interview Practice</h2>
            <div class="question">
                <h3>Practice answering these interview questions about sports:</h3>
                <p><em>Click on each question to see sample answers!</em></p>
            </div>
            <div id="interview-questions-container"></div>
            <div class="navigation">
                <button class="btn" onclick="showFinalResults()">🎉 Show Final Results</button>
            </div>
        </div>

        <!-- Final Results -->
        <div id="final-results" class="section">
            <h2>🎊 Congratulations!</h2>
            <div class="score-display" id="final-score-display"></div>
            <div class="navigation">
                <button class="btn" onclick="restartApp()">🔄 Start Over</button>
            </div>
        </div>

        <div class="progress">
            <div class="progress-bar" id="progress-bar" style="width: 14.28%"></div>
        </div>
    </div>

    <script>
        // Text content from Unit 7c - Any Ideas? (Sports Email)
        const fullText = `Hi Dave,
You've asked me to give you my opinion on which sport you should take up: football or snowboarding. They are both expensive sports. However, kayaking is my response to play, but it can be dangerous. You can easily get hurt if you're not careful or experienced. You can easily see, it's very demanding physically. You need to buy a lot of equipment such as a snowboard, fitness gear or goggles, and equipment such as a snowboard. You should choose the one that is not only fun but also makes you feel fulfilled. I think football is the most popular sport.
Good luck.
Mike

Sports can be exciting, challenging, expensive, boring, difficult, or thrilling. Some sports like cycling and skiing are very popular. Others like motocross can be dangerous but exciting. When choosing a sport, think about what makes you happy and fulfilled.`;

        // Global variables
        let readingTimer;
        let currentWordIndex = 0;
        let words = [];
        let sentences = [];
        let isReading = false;
        let comprehensionAnswers = [];
        let userScores = {
            comprehension: 0,
            capital: 0,
            total: 0
        };

        // Initialize the app
        function initializeApp() {
            setupSpeedReading();
            setupComprehensionQuestions();
            setupGapFill();
            setupCapitalLetters();
            setupSentenceOrder();
            setupInterview();
        }

        // Speed Reading Functions
        function setupSpeedReading() {
            words = fullText.split(/\s+/);
            sentences = fullText.split(/[.!?]+/).filter(s => s.trim().length > 0);
            document.getElementById('reading-text').innerHTML = fullText;
        }

        function startReading() {
            if (isReading) return;
            
            isReading = true;
            const wpm = parseInt(document.getElementById('wpm').value);
            const highlightMode = document.getElementById('highlight-mode').value;
            const interval = 60000 / wpm; // milliseconds per word
            
            readingTimer = setInterval(() => {
                highlightWords(highlightMode);
            }, interval);
        }

        function highlightWords(mode) {
            const textContainer = document.getElementById('reading-text');
            let wordsToHighlight = 1;
            
            if (mode === 'sentence') {
                // Find current sentence
                let wordCount = 0;
                for (let i = 0; i < sentences.length; i++) {
                    const sentenceWords = sentences[i].trim().split(/\s+/).length;
                    if (currentWordIndex < wordCount + sentenceWords) {
                        highlightSentence(i);
                        currentWordIndex = wordCount + sentenceWords;
                        break;
                    }
                    wordCount += sentenceWords;
                }
            } else {
                wordsToHighlight = parseInt(mode);
                highlightWordGroup(wordsToHighlight);
            }
            
            if (currentWordIndex >= words.length) {
                pauseReading();
            }
        }

        function highlightWordGroup(count) {
            const textContainer = document.getElementById('reading-text');
            let html = '';
            let wordIndex = 0;
            
            const wordsArray = fullText.split(/(\s+)/);
            
            for (let i = 0; i < wordsArray.length; i++) {
                if (wordsArray[i].trim().length > 0) {
                    if (wordIndex >= currentWordIndex && wordIndex < currentWordIndex + count) {
                        html += `<span class="highlight">${wordsArray[i]}</span>`;
                    } else {
                        html += wordsArray[i];
                    }
                    wordIndex++;
                } else {
                    html += wordsArray[i];
                }
            }
            
            textContainer.innerHTML = html;
            currentWordIndex += count;
        }

        function highlightSentence(sentenceIndex) {
            const textContainer = document.getElementById('reading-text');
            let html = fullText;
            
            if (sentences[sentenceIndex]) {
                const sentence = sentences[sentenceIndex].trim();
                html = html.replace(sentence, `<span class="highlight">${sentence}</span>`);
            }
            
            textContainer.innerHTML = html;
        }

        function pauseReading() {
            isReading = false;
            if (readingTimer) {
                clearInterval(readingTimer);
            }
        }

        function resetReading() {
            pauseReading();
            currentWordIndex = 0;
            document.getElementById('reading-text').innerHTML = fullText;
        }

        // Comprehension Questions
        function setupComprehensionQuestions() {
            const questions = [
                {
                    question: "Who is Mike writing to?",
                    options: ["Ann", "Dave", "Jane", "Laura"],
                    correct: 1
                },
                {
                    question: "What two sports is Dave choosing between?",
                    options: ["Cycling and skiing", "Football and snowboarding", "Kayaking and motocross", "Tennis and basketball"],
                    correct: 1
                },
                {
                    question: "What does Mike say about both sports?",
                    options: ["They are easy", "They are boring", "They are expensive", "They are safe"],
                    correct: 2
                },
                {
                    question: "What sport does Mike suggest as an alternative?",
                    options: ["Football", "Snowboarding", "Kayaking", "Cycling"],
                    correct: 2
                },
                {
                    question: "What does Mike say about kayaking?",
                    options: ["It's safe and easy", "It can be dangerous", "It's very cheap", "It's boring"],
                    correct: 1
                },
                {
                    question: "What do you need for snowboarding according to Mike?",
                    options: ["Just a snowboard", "A lot of equipment", "Only goggles", "Nothing special"],
                    correct: 1
                },
                {
                    question: "What should Dave choose according to Mike?",
                    options: ["The cheapest sport", "The easiest sport", "Something fun and fulfilling", "The most popular sport"],
                    correct: 2
                },
                {
                    question: "What does Mike think about football?",
                    options: ["It's dangerous", "It's the most popular sport", "It's too expensive", "It's boring"],
                    correct: 1
                },
                {
                    question: "What adjectives can describe sports according to the text?",
                    options: ["Only exciting", "Exciting, challenging, expensive", "Just dangerous", "Only popular"],
                    correct: 1
                },
                {
                    question: "What should you think about when choosing a sport?",
                    options: ["Only the cost", "What makes you happy", "What others think", "Only safety"],
                    correct: 1
                }
            ];

            const container = document.getElementById('questions-container');
            questions.forEach((q, index) => {
                const questionDiv = document.createElement('div');
                questionDiv.className = 'question';
                questionDiv.innerHTML = `
                    <h3>Question ${index + 1}</h3>
                    <p><strong>${q.question}</strong></p>
                    <div class="options">
                        ${q.options.map((option, optIndex) => `
                            <label class="option" onclick="selectOption(${index}, ${optIndex})">
                                <input type="radio" name="q${index}" value="${optIndex}" style="margin-right: 10px;">
                                ${String.fromCharCode(97 + optIndex)}) ${option}
                            </label>
                        `).join('')}
                    </div>
                `;
                container.appendChild(questionDiv);
            });

            comprehensionAnswers = questions.map(q => q.correct);
        }

        function selectOption(questionIndex, optionIndex) {
            const options = document.querySelectorAll(`input[name="q${questionIndex}"]`);
            options.forEach(opt => opt.checked = false);
            options[optionIndex].checked = true;
            
            const labels = document.querySelectorAll(`input[name="q${questionIndex}"]`).forEach((input, idx) => {
                input.parentElement.classList.remove('selected');
            });
            options[optionIndex].parentElement.classList.add('selected');
        }

        function checkAnswers() {
            let correct = 0;
            for (let i = 0; i < comprehensionAnswers.length; i++) {
                const selected = document.querySelector(`input[name="q${i}"]:checked`);
                if (selected && parseInt(selected.value) === comprehensionAnswers[i]) {
                    correct++;
                    selected.parentElement.classList.add('correct');
                } else if (selected) {
                    selected.parentElement.classList.add('incorrect');
                }
            }
            
            userScores.comprehension = Math.round((correct / comprehensionAnswers.length) * 100);
            document.getElementById('comprehension-score').textContent = 
                `Score: ${correct}/${comprehensionAnswers.length} (${userScores.comprehension}%)`;
        }

        function showAnswers() {
            for (let i = 0; i < comprehensionAnswers.length; i++) {
                const correctOption = document.querySelector(`input[name="q${i}"][value="${comprehensionAnswers[i]}"]`);
                if (correctOption) {
                    correctOption.parentElement.classList.add('correct');
                }
            }
        }

        // Gap Fill Functions
        function setupGapFill() {
            // Define words in the order they appear in the text
            const gapWordsInOrder = [
                { word: 'opinion', position: 'give you my _____' },
                { word: 'football', position: 'which sport: _____ or snowboarding' },
                { word: 'expensive', position: 'They are both _____ sports' },
                { word: 'kayaking', position: '_____ is my response to play' },
                { word: 'dangerous', position: 'it can be _____' },
                { word: 'experienced', position: 'careful or _____' },
                { word: 'equipment', position: 'buy a lot of _____' },
                { word: 'fulfilled', position: 'makes you feel _____' },
                { word: 'popular', position: 'the most _____ sport' },
                { word: 'exciting', position: 'Sports can be _____' },
                { word: 'challenging', position: '_____, expensive, boring' },
                { word: 'happy', position: 'what makes you _____' }
            ];

            const wordBank = document.getElementById('word-bank');

            // Shuffle the words for the word bank
            const shuffledWords = [...gapWordsInOrder].sort(() => Math.random() - 0.5);
            shuffledWords.forEach(item => {
                const wordElement = document.createElement('span');
                wordElement.className = 'word-item';
                wordElement.textContent = item.word;
                wordBank.appendChild(wordElement);
            });

            // Create gap text by replacing words in order they appear
            let gapText = fullText;
            gapWordsInOrder.forEach((item, index) => {
                // Use a more specific regex to match the exact word
                const regex = new RegExp(`\\b${item.word}\\b`, 'i');
                gapText = gapText.replace(regex,
                    `<span class="gap" onclick="revealGap(${index})" data-answer="${item.word}">${index + 1}</span>`);
            });

            document.getElementById('gap-text').innerHTML = gapText;
            window.gapAnswers = gapWordsInOrder.map(item => item.word);
        }

        function revealGap(index) {
            // Find the specific gap by its data-answer attribute
            const targetGap = document.querySelector(`.gap[onclick="revealGap(${index})"]`);
            if (targetGap && !targetGap.classList.contains('revealed')) {
                targetGap.classList.add('revealed');
                targetGap.textContent = window.gapAnswers[index];
                targetGap.onclick = null; // Remove click handler after revealing
            }
        }

        function revealAllGaps() {
            for (let i = 0; i < window.gapAnswers.length; i++) {
                revealGap(i);
            }
        }

        function resetGapFill() {
            // Reset the gap fill section
            setupGapFill();
        }

        // Capital Letters Functions
        function setupCapitalLetters() {
            const capitalQuestions = [
                {
                    question: "mike wrote an email to dave about sports.",
                    correct: "Mike wrote an email to Dave about sports.",
                    options: ["mike wrote an email to dave about sports.", "Mike wrote an email to dave about sports.", "Mike wrote an email to Dave about sports.", "mike Wrote An Email To Dave About Sports."]
                },
                {
                    question: "football and snowboarding are expensive sports.",
                    correct: "Football and snowboarding are expensive sports.",
                    options: ["football and snowboarding are expensive sports.", "Football and snowboarding are expensive sports.", "Football And Snowboarding Are Expensive Sports.", "football And Snowboarding are Expensive Sports."]
                },
                {
                    question: "people in canada love hockey and skiing.",
                    correct: "People in Canada love hockey and skiing.",
                    options: ["people in canada love hockey and skiing.", "People in canada love hockey and skiing.", "People in Canada love hockey and skiing.", "people In Canada Love Hockey And Skiing."]
                },
                {
                    question: "i think cycling is very exciting.",
                    correct: "I think cycling is very exciting.",
                    options: ["i think cycling is very exciting.", "I think cycling is very exciting.", "I Think Cycling Is Very Exciting.", "i Think Cycling is Very Exciting."]
                },
                {
                    question: "motocross racing happens on saturday.",
                    correct: "Motocross racing happens on Saturday.",
                    options: ["motocross racing happens on saturday.", "Motocross racing happens on saturday.", "Motocross racing happens on Saturday.", "motocross Racing Happens On Saturday."]
                }
            ];

            const container = document.getElementById('capital-questions-container');
            capitalQuestions.forEach((q, index) => {
                const questionDiv = document.createElement('div');
                questionDiv.className = 'question';
                questionDiv.innerHTML = `
                    <h3>Question ${index + 1}</h3>
                    <p><strong>Add capital letters to: "${q.question}"</strong></p>
                    <div class="options">
                        ${q.options.map((option, optIndex) => `
                            <label class="option" onclick="selectCapitalOption(${index}, ${optIndex})">
                                <input type="radio" name="capital${index}" value="${optIndex}" style="margin-right: 10px;">
                                ${String.fromCharCode(97 + optIndex)}) ${option}
                            </label>
                        `).join('')}
                    </div>
                `;
                container.appendChild(questionDiv);
            });

            window.capitalAnswers = capitalQuestions.map(q => q.options.indexOf(q.correct));
        }

        function selectCapitalOption(questionIndex, optionIndex) {
            const options = document.querySelectorAll(`input[name="capital${questionIndex}"]`);
            options.forEach(opt => {
                opt.checked = false;
                opt.parentElement.classList.remove('selected');
            });
            options[optionIndex].checked = true;
            options[optionIndex].parentElement.classList.add('selected');
        }

        function checkCapitalAnswers() {
            let correct = 0;
            for (let i = 0; i < window.capitalAnswers.length; i++) {
                const selected = document.querySelector(`input[name="capital${i}"]:checked`);
                if (selected && parseInt(selected.value) === window.capitalAnswers[i]) {
                    correct++;
                    selected.parentElement.classList.add('correct');
                } else if (selected) {
                    selected.parentElement.classList.add('incorrect');
                }
            }

            userScores.capital = Math.round((correct / window.capitalAnswers.length) * 100);
            document.getElementById('capital-score').textContent =
                `Score: ${correct}/${window.capitalAnswers.length} (${userScores.capital}%)`;
        }

        function showCapitalAnswers() {
            for (let i = 0; i < window.capitalAnswers.length; i++) {
                const correctOption = document.querySelector(`input[name="capital${i}"][value="${window.capitalAnswers[i]}"]`);
                if (correctOption) {
                    correctOption.parentElement.classList.add('correct');
                }
            }
        }

        // Sentence Ordering Functions
        function setupSentenceOrder() {
            const sentenceParts = [
                "Hi Dave,",
                "You've asked me to give you my opinion on which sport you should take up.",
                "They are both expensive sports.",
                "However, kayaking is my response to play, but it can be dangerous.",
                "You need to buy a lot of equipment such as a snowboard.",
                "You should choose the one that is not only fun but also makes you feel fulfilled.",
                "I think football is the most popular sport.",
                "Good luck. Mike"
            ];

            const shuffled = [...sentenceParts].sort(() => Math.random() - 0.5);
            const sourceContainer = document.getElementById('sentence-source');
            const targetContainer = document.getElementById('sentence-target');

            shuffled.forEach((sentence, index) => {
                const sentenceElement = document.createElement('div');
                sentenceElement.className = 'sentence-item';
                sentenceElement.textContent = sentence;
                sentenceElement.draggable = true;
                sentenceElement.dataset.original = sentenceParts.indexOf(sentence);
                
                sentenceElement.addEventListener('dragstart', handleDragStart);
                sentenceElement.addEventListener('dragend', handleDragEnd);
                
                sourceContainer.appendChild(sentenceElement);
            });

            // Create drop zones
            for (let i = 0; i < 8; i++) {
                const dropZone = document.createElement('div');
                dropZone.className = 'drop-zone';
                dropZone.textContent = `Position ${i + 1}`;
                dropZone.dataset.position = i;
                
                dropZone.addEventListener('dragover', handleDragOver);
                dropZone.addEventListener('drop', handleDrop);
                dropZone.addEventListener('dragleave', handleDragLeave);
                
                targetContainer.appendChild(dropZone);
            }

            window.correctOrder = sentenceParts;
        }

        function handleDragStart(e) {
            e.target.classList.add('dragging');
            e.dataTransfer.setData('text/plain', e.target.textContent);
            e.dataTransfer.setData('application/json', JSON.stringify({
                text: e.target.textContent,
                original: e.target.dataset.original
            }));
        }

        function handleDragEnd(e) {
            e.target.classList.remove('dragging');
        }

        function handleDragOver(e) {
            e.preventDefault();
            e.target.classList.add('drag-over');
        }

        function handleDragLeave(e) {
            e.target.classList.remove('drag-over');
        }

        function handleDrop(e) {
            e.preventDefault();
            e.target.classList.remove('drag-over');
            
            const data = JSON.parse(e.dataTransfer.getData('application/json'));
            e.target.textContent = data.text;
            e.target.dataset.original = data.original;
            e.target.style.background = '#e8f4fd';
        }

        function checkOrder() {
            const dropZones = document.querySelectorAll('.drop-zone');
            let correct = 0;
            
            dropZones.forEach((zone, index) => {
                if (zone.dataset.original == index) {
                    zone.style.background = '#d4edda';
                    correct++;
                } else {
                    zone.style.background = '#f8d7da';
                }
            });
            
            alert(`You got ${correct}/8 sentences in the correct order!`);
        }

        // Interview Practice Functions
        function setupInterview() {
            const interviewQuestions = [
                {
                    question: "What is your favorite sport?",
                    sampleAnswers: [
                        "I love football because it's exciting and popular.",
                        "I enjoy cycling because it's challenging but not too expensive.",
                        "I think kayaking is thrilling, even though it can be dangerous."
                    ]
                },
                {
                    question: "Do you think some sports are too expensive?",
                    sampleAnswers: [
                        "Yes, snowboarding requires a lot of expensive equipment.",
                        "I think most sports can be affordable if you buy used equipment.",
                        "Some sports like motocross are very expensive to start."
                    ]
                },
                {
                    question: "What makes a sport fulfilling for you?",
                    sampleAnswers: [
                        "I like sports that are challenging and make me feel accomplished.",
                        "A sport is fulfilling when it's fun and keeps me healthy.",
                        "I enjoy sports where I can improve my skills over time."
                    ]
                }
            ];

            const container = document.getElementById('interview-questions-container');
            interviewQuestions.forEach((q, index) => {
                const questionDiv = document.createElement('div');
                questionDiv.className = 'question';
                questionDiv.innerHTML = `
                    <h3>Question ${index + 1}</h3>
                    <p><strong>${q.question}</strong></p>
                    <button class="btn" onclick="toggleAnswers(${index})" style="margin: 10px 0;">Show Sample Answers</button>
                    <div id="answers-${index}" style="display: none; margin-top: 15px;">
                        <h4>Sample Answers:</h4>
                        <ul>
                            ${q.sampleAnswers.map(answer => `<li style="margin: 8px 0; padding: 8px; background: #f8f9fa; border-radius: 5px;">${answer}</li>`).join('')}
                        </ul>
                        <p style="margin-top: 15px; font-style: italic; color: #666;">Now try to answer this question yourself!</p>
                    </div>
                `;
                container.appendChild(questionDiv);
            });
        }

        function toggleAnswers(questionIndex) {
            const answersDiv = document.getElementById(`answers-${questionIndex}`);
            const button = event.target;

            if (answersDiv.style.display === 'none') {
                answersDiv.style.display = 'block';
                button.textContent = 'Hide Sample Answers';
            } else {
                answersDiv.style.display = 'none';
                button.textContent = 'Show Sample Answers';
            }
        }



        // Navigation and Progress Functions
        function showSection(sectionId) {
            // Hide all sections
            document.querySelectorAll('.section').forEach(section => {
                section.classList.remove('active');
            });
            
            // Show selected section
            document.getElementById(sectionId).classList.add('active');
            
            // Update progress bar
            const sections = ['speed-reading', 'comprehension', 'gap-fill', 'capital-letters', 'sentence-order', 'interview', 'final-results'];
            const currentIndex = sections.indexOf(sectionId);
            const progress = ((currentIndex + 1) / sections.length) * 100;
            document.getElementById('progress-bar').style.width = progress + '%';
        }

        function showFinalResults() {
            userScores.total = Math.round((userScores.comprehension + userScores.capital) / 2);

            let message = '';
            if (userScores.total >= 90) {
                message = '🌟 Excellent! You are a reading superstar!';
            } else if (userScores.total >= 80) {
                message = '🎉 Great job! You have strong reading skills!';
            } else if (userScores.total >= 70) {
                message = '👍 Good work! Keep practicing to improve!';
            } else {
                message = '📚 Keep reading and practicing! You can do it!';
            }

            document.getElementById('final-score-display').innerHTML = `
                <h3>${message}</h3>
                <p>Comprehension Score: ${userScores.comprehension}%</p>
                <p>Capital Letters Score: ${userScores.capital}%</p>
                <p><strong>Overall Score: ${userScores.total}%</strong></p>
                <p style="margin-top: 20px; font-style: italic;">Great job completing the Unit 7c - Any Ideas? activities! You've practiced reading comprehension, sports vocabulary, and grammar skills.</p>
            `;

            showSection('final-results');
        }

        function restartApp() {
            // Reset all scores
            userScores = { comprehension: 0, capital: 0, total: 0 };
            
            // Reset reading
            resetReading();
            
            // Clear all selections
            document.querySelectorAll('input[type="radio"]').forEach(input => {
                input.checked = false;
                input.parentElement.classList.remove('selected', 'correct', 'incorrect');
            });
            
            // Reset gap fill
            document.querySelectorAll('.gap').forEach((gap, index) => {
                gap.classList.remove('revealed');
                const gapNumber = gap.getAttribute('onclick')?.match(/revealGap\((\d+)\)/)?.[1];
                if (gapNumber) {
                    gap.textContent = parseInt(gapNumber) + 1;
                    gap.onclick = () => revealGap(parseInt(gapNumber));
                }
            });
            
            // Reset sentence order
            const sourceContainer = document.getElementById('sentence-source');
            const targetContainer = document.getElementById('sentence-target');
            const dropZones = document.querySelectorAll('.drop-zone');
            
            dropZones.forEach((zone, index) => {
                if (zone.dataset.original !== undefined) {
                    const sentenceElement = document.createElement('div');
                    sentenceElement.className = 'sentence-item';
                    sentenceElement.textContent = zone.textContent;
                    sentenceElement.draggable = true;
                    sentenceElement.dataset.original = zone.dataset.original;
                    
                    sentenceElement.addEventListener('dragstart', handleDragStart);
                    sentenceElement.addEventListener('dragend', handleDragEnd);
                    
                    sourceContainer.appendChild(sentenceElement);
                }
                
                zone.textContent = `Position ${index + 1}`;
                zone.style.background = '';
                delete zone.dataset.original;
            });
            
            // Clear scores
            document.getElementById('comprehension-score').textContent = '';
            document.getElementById('capital-score').textContent = '';
            
            // Go back to first section
            showSection('speed-reading');
        }

        // Initialize the app when page loads
        window.onload = function() {
            initializeApp();
        };
    </script>
</body>
</html>