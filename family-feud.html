<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Family Feud - Module 6&7 Review: Health & Sports</title>
    <style>
        body {
            font-family: 'Comic Sans MS', 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #FFB6C1 0%, #87CEEB 100%);
            color: #333;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(45deg, #FFA07A, #FFB6C1);
            color: #333;
            border-radius: 15px;
            border: 3px solid #FFF;
        }

        .question-display {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 30px;
            padding: 25px;
            background: #E6F3FF;
            border: 3px dashed #87CEEB;
            border-radius: 15px;
            color: #333;
        }

        .answers-board {
            background: #F0F8FF;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 3px solid #87CEEB;
        }

        .answer-slot {
            display: flex;
            align-items: center;
            background: #4169E1;
            color: white;
            margin: 10px 0;
            padding: 15px 20px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 3px solid transparent;
        }

        .answer-slot:hover {
            background: #1E90FF;
            transform: scale(1.02);
            border-color: #FFD700;
        }

        .answer-slot.revealed {
            background: #32CD32;
            cursor: default;
        }

        .answer-number {
            background: #FFD700;
            color: #333;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 18px;
            margin-right: 20px;
            flex-shrink: 0;
        }

        .answer-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }

        .answer-text {
            font-size: 20px;
            font-weight: bold;
        }

        .answer-points {
            font-size: 18px;
            font-weight: bold;
            background: #FF6347;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
        }

        .hidden-answer {
            visibility: hidden;
        }

        .category-selector {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .category-btn {
            background: linear-gradient(45deg, #FF69B4, #FF1493);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            font-weight: bold;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 3px solid transparent;
        }

        .category-btn:hover {
            transform: translateY(-5px);
            border-color: #FFD700;
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .game-controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .control-btn {
            background: #FF6347;
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 3px solid transparent;
        }

        .control-btn:hover {
            background: #FF4500;
            transform: translateY(-2px);
            border-color: #FFD700;
        }

        .strike-system {
            display: none;
            text-align: center;
            margin: 20px 0;
        }

        .strikes {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 15px 0;
        }

        .strike {
            width: 60px;
            height: 60px;
            background: #ddd;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 30px;
            font-weight: bold;
            border: 3px solid #999;
        }

        .strike.active {
            background: #FF0000;
            color: white;
            border-color: #8B0000;
        }

        .score-display {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            color: #4169E1;
            margin: 20px 0;
            padding: 15px;
            background: #E6F3FF;
            border-radius: 10px;
            border: 2px solid #87CEEB;
        }

        .completion-message {
            display: none;
            text-align: center;
            padding: 30px;
            background: linear-gradient(45deg, #98FB98, #90EE90);
            border-radius: 15px;
            margin-top: 20px;
            border: 3px solid #32CD32;
        }

        .back-btn {
            background: #32CD32;
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            text-decoration: none;
            display: inline-block;
            margin-bottom: 20px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: #228B22;
            text-decoration: none;
            color: white;
            transform: translateY(-2px);
        }

        .game-instructions {
            background: #FFFACD;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border: 2px solid #F0E68C;
        }

        @media (max-width: 768px) {
            .category-selector {
                grid-template-columns: 1fr;
            }
            
            .game-controls {
                flex-direction: column;
                align-items: center;
            }
            
            .answer-slot {
                padding: 12px 15px;
            }
            
            .answer-text {
                font-size: 16px;
            }
            
            .question-display {
                font-size: 18px;
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="lesson_plan_main.html" class="back-btn">← Main Menu</a>
        <div class="header">
            <h1>Module 6&7 Review: Health & Sports</h1>
            <p>Click the boxes to reveal vocabulary and phrases from the units!</p>
            <p><strong>Find items worth: 5 • 15 • 20 • 25 • 30 points</strong></p>
        </div>

        <div class="game-instructions">
            <h3>🎮 How to Play:</h3>
            <p>1. Choose a category below</p>
            <p>2. Click on the numbered boxes to reveal answers</p>
            <p>3. Try to find all 5 answers to get the highest score!</p>
            <p>4. Each wrong guess gives you a strike (3 strikes = game over)</p>
        </div>

        <div class="category-selector" id="categorySelector">
            <!-- Categories will be generated here -->
        </div>

        <div class="question-display" id="questionDisplay">
            Select a category to start playing!
        </div>

        <div class="score-display" id="totalScore">
            Total Score: 0 points
        </div>

        <div class="strike-system" id="strikeSystem">
            <h3>❌ Strikes</h3>
            <div class="strikes">
                <div class="strike" id="strike1">X</div>
                <div class="strike" id="strike2">X</div>
                <div class="strike" id="strike3">X</div>
            </div>
        </div>

        <div class="answers-board" id="answersBoard">
            <!-- Answer slots will be generated here -->
        </div>

        <div class="game-controls">
            <button class="control-btn" onclick="addStrike()">❌ Add Strike</button>
            <button class="control-btn reveal-all-btn" onclick="revealAll()">⚡ Reveal All</button>
            <button class="control-btn reset-btn" onclick="resetRound()">🔄 New Round</button>
            <button class="control-btn" onclick="window.close()">✅ Finish Game</button>
        </div>

        <div class="completion-message" id="completionMessage">
            <!-- Completion message will appear here -->
        </div>
    </div>

    <script>
        const gameData = {
            'Health Problems': {
                question: 'Find common health problems.',
                answers: [
                    { text: 'Stomachache', points: 30 },
                    { text: 'Headache', points: 25 },
                    { text: 'Sore throat', points: 20 },
                    { text: 'Fever', points: 15 },
                    { text: 'Cough', points: 5 }
                ]
            },
            'Sports Equipment': {
                question: 'Find sports equipment.',
                answers: [
                    { text: 'Snowboard', points: 30 },
                    { text: 'Paddle', points: 25 },
                    { text: 'Helmet', points: 20 },
                    { text: 'Goggles', points: 15 },
                    { text: 'Gloves', points: 5 }
                ]
            },
            'Sports Adjectives': {
                question: 'Find adjectives that describe sports.',
                answers: [
                    { text: 'Expensive', points: 30 },
                    { text: 'Dangerous', points: 25 },
                    { text: 'Exciting', points: 20 },
                    { text: 'Challenging', points: 15 },
                    { text: 'Thrilling', points: 5 }
                ]
            },
            'Transportation': {
                question: 'Find types of transportation.',
                answers: [
                    { text: 'Gondola', points: 30 },
                    { text: 'Snowmobile', points: 25 },
                    { text: 'Rickshaw', points: 20 },
                    { text: 'Helicopter', points: 15 },
                    { text: 'Hovercraft', points: 5 }
                ]
            },
            'Health Dialog Phrases': {
                question: 'Find phrases used when talking about health.',
                answers: [
                    { text: 'Are you OK?', points: 30 },
                    { text: 'I feel awful', points: 25 },
                    { text: 'What is wrong?', points: 20 },
                    { text: 'You should see a doctor', points: 15 },
                    { text: 'Take an aspirin', points: 5 }
                ]
            }
        };

        let currentCategory = '';
        let currentAnswers = [];
        let revealedCount = 0;
        let totalScore = 0;
        let strikes = 0;
        let nextAnswerIndex = 0;

        function initializeGame() {
            createCategorySelector();
        }

        function createCategorySelector() {
            const selector = document.getElementById('categorySelector');
            selector.innerHTML = '';
            
            Object.keys(gameData).forEach(category => {
                const btn = document.createElement('div');
                btn.className = 'category-btn';
                btn.textContent = category;
                btn.onclick = () => selectCategory(category);
                selector.appendChild(btn);
            });
        }

        function selectCategory(category) {
            currentCategory = category;
            currentAnswers = gameData[category].answers;
            revealedCount = 0;
            totalScore = 0;
            strikes = 0;
            nextAnswerIndex = 0;
            
            document.getElementById('questionDisplay').textContent = gameData[category].question;
            document.getElementById('categorySelector').style.display = 'none';
            document.getElementById('strikeSystem').style.display = 'block';
            
            updateTotalScore();
            updateStrikes();
            
            createAnswerBoard();
            
            document.getElementById('completionMessage').style.display = 'none';
        }

        function createAnswerBoard() {
            const board = document.getElementById('answersBoard');
            board.innerHTML = '';
            
            currentAnswers.forEach((answer, index) => {
                const slot = document.createElement('div');
                slot.className = 'answer-slot';
                slot.onclick = () => revealAnswer(index);
                
                slot.innerHTML = `
                    <div class="answer-number">${index + 1}</div>
                    <div class="answer-content">
                        <div class="answer-text hidden-answer">${answer.text}</div>
                        <div class="answer-points hidden-answer">${answer.points}</div>
                    </div>
                `;
                board.appendChild(slot);
            });
        }

        function revealAnswer(index) {
            const slots = document.querySelectorAll('.answer-slot');
            const slot = slots[index];
            
            if (!slot.classList.contains('revealed')) {
                slot.classList.add('revealed');
                const answerText = slot.querySelector('.answer-text');
                const answerPoints = slot.querySelector('.answer-points');
                answerText.classList.remove('hidden-answer');
                answerPoints.classList.remove('hidden-answer');
                
                totalScore += currentAnswers[index].points;
                updateTotalScore();
                revealedCount++;
                
                if (revealedCount === currentAnswers.length) {
                    setTimeout(() => {
                        showCompletionMessage();
                    }, 500);
                }
            }
        }

        function updateTotalScore() {
            document.getElementById('totalScore').textContent = `Total Score: ${totalScore} points`;
        }

        function updateStrikes() {
            for (let i = 1; i <= 3; i++) {
                const strike = document.getElementById(`strike${i}`);
                if (i <= strikes) {
                    strike.classList.add('active');
                } else {
                    strike.classList.remove('active');
                }
            }
        }

        function addStrike() {
            if (strikes < 3) {
                strikes++;
                updateStrikes();
                
                if (strikes === 3) {
                    setTimeout(() => {
                        alert('Game Over! 3 strikes - try again!');
                        resetRound();
                    }, 500);
                }
            }
        }

        function revealAll() {
            const slots = document.querySelectorAll('.answer-slot');
            let delay = 0;
            
            slots.forEach((slot, index) => {
                if (!slot.classList.contains('revealed')) {
                    setTimeout(() => {
                        slot.classList.add('revealed');
                        const answerText = slot.querySelector('.answer-text');
                        const answerPoints = slot.querySelector('.answer-points');
                        answerText.classList.remove('hidden-answer');
                        answerPoints.classList.remove('hidden-answer');
                        
                        totalScore += currentAnswers[index].points;
                        updateTotalScore();
                        revealedCount++;
                    }, delay);
                    delay += 300;
                }
            });
            
            setTimeout(() => {
                showCompletionMessage();
            }, delay + 500);
        }

        function showCompletionMessage() {
            const message = document.getElementById('completionMessage');
            let performanceMsg = '';
            
            if (totalScore >= 90) {
                performanceMsg = '🌟 Wow! You did amazing!';
            } else if (totalScore >= 70) {
                performanceMsg = '👍 Great job! You know the vocabulary well!';
            } else if (totalScore >= 50) {
                performanceMsg = '😊 Good work! Keep practicing!';
            } else {
                performanceMsg = '📚 Keep studying! You can do better!';
            }
            
            message.innerHTML = `
                <h2>🎉 Round Complete! 🎉</h2>
                <h3>${performanceMsg}</h3>
                <p><strong>Final Score: ${totalScore} points</strong></p>
                <p>Category: ${currentCategory}</p>
                <button class="control-btn" onclick="resetRound()">🔄 Play Again</button>
            `;
            message.style.display = 'block';
        }

        function resetRound() {
            currentCategory = '';
            currentAnswers = [];
            revealedCount = 0;
            totalScore = 0;
            strikes = 0;
            nextAnswerIndex = 0;
            
            document.getElementById('categorySelector').style.display = 'grid';
            document.getElementById('strikeSystem').style.display = 'none';
            
            updateTotalScore();
            
            document.getElementById('questionDisplay').textContent = 'Select a category to start playing!';
            document.getElementById('answersBoard').innerHTML = '';
            document.getElementById('strikeSystem').style.display = 'none';
            document.getElementById('completionMessage').style.display = 'none';
        }

        window.onload = function() {
            initializeGame();
        };
    </script>
</body>
</html>
