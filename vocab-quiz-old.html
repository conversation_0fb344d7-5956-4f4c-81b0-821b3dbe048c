<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fun Word Game! - Module 6&7 Review: Health & Sports</title>
    <style>
        body {
            font-family: 'Comic Sans MS', cursive, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f0f9ff;
            color: #333;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #FFD54F;
            color: #333;
            border-radius: 15px;
        }

        .header h1 {
            font-size: 24px;
            margin: 0;
        }

        .quiz-box {
            background: #fff;
            border: 3px solid #4CAF50;
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
        }

        .question-container {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .question-text {
            font-size: 18px;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .image-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin: 15px 0;
        }

        .choice-box {
            background: white;
            border: 3px solid #2196F3;
            border-radius: 15px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .choice-box:hover {
            transform: scale(1.02);
            background: #e3f2fd;
        }

        .choice-box img {
            width: 60px;
            height: 60px;
            border-radius: 10px;
            object-fit: cover;
        }

        .choice-box.correct {
            background: #a5d6a7;
            border-color: #4CAF50;
        }

        .choice-box.wrong {
            background: #ffcdd2;
            border-color: #f44336;
        }

        .score-bar {
            background: #FFE082;
            border-radius: 25px;
            padding: 10px 20px;
            font-size: 18px;
            text-align: center;
            margin: 15px 0;
        }

        .next-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            display: block;
            margin: 20px auto;
            font-family: 'Comic Sans MS', cursive, sans-serif;
        }

        .next-button:hover {
            transform: scale(1.05);
        }

        .next-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .back-btn {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            display: inline-block;
            margin-bottom: 20px;
            font-family: 'Comic Sans MS', cursive, sans-serif;
        }

        .celebration {
            text-align: center;
            padding: 20px;
            background: #FFF176;
            border-radius: 15px;
            margin: 20px 0;
        }

        .celebration h2 {
            font-size: 24px;
            margin: 10px 0;
        }

        .celebration .emoji {
            font-size: 48px;
            margin: 10px 0;
        }

        .progress-stars {
            display: flex;
            justify-content: center;
            gap: 5px;
            margin: 10px 0;
            font-size: 24px;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="lesson_plan_main.html" class="back-btn">← Back to Games</a>
        
        <div class="header">
            <h1>🎮 Fun Word Game! 🎮</h1>
            <p>Guess the words from Module 6&7: Health Problems, Sports, Injuries, and Transportation.</p>
        </div>

        <div class="quiz-box" id="quizContainer">
            <!-- Quiz content will be generated here -->
        </div>

        <div id="finalScore" style="display: none;">
            <!-- Final score will be shown here -->
        </div>
    </div>

    <script>
        const questions = [
            {
                question: "I’m tall and shiny with many windows. You see me in big cities. What am I? 🏙️",
                options: ["barn", "modern buildings", "fresh air", "maple syrup"],
                images: ["city.png", "city.png", "country.png", "syrup.png"],
                correct: 1,
                hint: "Yes! Modern buildings are part of city life."
            },
            {
                question: "People feel calm with me. You breathe me in the countryside. What am I? 🌬️",
                options: ["fresh air", "crowded streets", "traffic", "subway"],
                images: ["country.png", "city.png", "traffic.png", "subway.png"],
                correct: 0,
                hint: "Great! Fresh air is common in the country."
            },
            {
                question: "I’m a person who takes photos of animals in nature. Who am I? 📸",
                options: ["war reporter", "wildlife photographer", "chef", "police officer"],
                images: ["reporter.png", "photographer.png", "chef.png", "police.png"],
                correct: 1,
                hint: "Correct! Wildlife photographers work in nature."
            },
            {
                question: "I’m the opposite of lazy. What adjective am I? 💪",
                options: ["hardworking", "cowardly", "shy", "dishonest"],
                images: ["work.png", "fear.png", "shy.png", "lie.png"],
                correct: 0,
                hint: "Yes! Hardworking is the opposite of lazy."
            },
            {
                question: "At the ticket office you hear: “___ or round-trip?” Fill in the missing part. 🚆",
                options: ["One-way", "Who", "Where", "Why"],
                images: ["ticket.png", "who.png", "where.png", "why.png"],
                correct: 0,
                hint: "Right! One-way or round-trip?"
            }
        ];

        let currentQuestion = 0;
        let score = 0;
        let answered = false;

        function showQuestion() {
            const container = document.getElementById('quizContainer');
            const question = questions[currentQuestion];
            
            const progressStars = Array(questions.length).fill('⭐️').map((star, index) => 
                index < currentQuestion ? '⭐️' : '☆'
            ).join('');
            
            container.innerHTML = `
                <div class="progress-stars">${progressStars}</div>
                <div class="score-bar">Score: ${score} out of ${questions.length} ⭐️</div>
                <div class="question-container">
                    <div class="question-text">${question.question}</div>
                    <div class="image-grid">
                        ${question.options.map((option, index) => `
                            <div class="choice-box" onclick="selectAnswer(${index})">
                                <img src="${question.images[index]}" alt="${option}">
                                <span>${option}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
                <div id="hint" style="display: none; text-align: center; margin: 15px 0; font-size: 18px;"></div>
                <button class="next-button" id="nextBtn" onclick="nextQuestion()" disabled>
                    ${currentQuestion === questions.length - 1 ? '🎉 Finish Game!' : 'Next Question! →'}
                </button>
            `;
        }

        function selectAnswer(selectedIndex) {
            if (answered) return;
            
            const question = questions[currentQuestion];
            const choices = document.querySelectorAll('.choice-box');
            const hint = document.getElementById('hint');
            
            choices.forEach((choice, index) => {
                if (index === question.correct) {
                    choice.classList.add('correct');
                } else if (index === selectedIndex) {
                    choice.classList.add('wrong');
                }
            });
            
            if (selectedIndex === question.correct) {
                score++;
                hint.innerHTML = question.hint;
            } else {
                hint.innerHTML = "Try again next time! 💪";
            }
            
            hint.style.display = 'block';
            document.getElementById('nextBtn').disabled = false;
            answered = true;
        }

        function nextQuestion() {
            currentQuestion++;
            
            if (currentQuestion < questions.length) {
                showQuestion();
                answered = false;
            } else {
                showFinalScore();
            }
        }

        function showFinalScore() {
            const percentage = Math.round((score / questions.length) * 100);
            let message = '';
            let emoji = '';
            
            if (percentage >= 80) {
                message = "WOW! You're amazing at this game! 🌟";
                emoji = '🏆';
            } else if (percentage >= 60) {
                message = "Great job! You're learning so well! 👍";
                emoji = '🌟';
            } else {
                message = "Good try! Let's play again and learn more! 💪";
                emoji = '✨';
            }
            
            document.getElementById('quizContainer').style.display = 'none';
            document.getElementById('finalScore').style.display = 'block';
            document.getElementById('finalScore').innerHTML = `
                <div class="celebration">
                    <div class="emoji">${emoji}</div>
                    <h2>Game Complete!</h2>
                    <div style="font-size: 24px;">You got ${score} out of ${questions.length} stars!</div>
                    <div style="font-size: 36px; margin: 15px 0;">
                        ${Array(score).fill('⭐️').join('')}
                    </div>
                    <p style="font-size: 18px;">${message}</p>
                    <button class="next-button" onclick="restartQuiz()">
                        🎮 Play Again!
                    </button>
                </div>
            `;
        }

        function restartQuiz() {
            currentQuestion = 0;
            score = 0;
            answered = false;
            document.getElementById('quizContainer').style.display = 'block';
            document.getElementById('finalScore').style.display = 'none';
            showQuestion();
        }

        // Start the quiz when page loads
        window.onload = showQuestion;
    </script>
</body>
</html> 