<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>60-Minute Interactive Lesson Plan - Module 1 Review: Lifestyles</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4, #45B7D1, #96CEB4);
            background-size: 400% 400%;
            animation: gradientShift 10s ease infinite;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
        }

        .timer-display {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #ff4757;
            color: white;
            padding: 15px 20px;
            border-radius: 50px;
            font-size: 20px;
            font-weight: bold;
            z-index: 1000;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }

        .phase-container {
            margin-bottom: 40px;
            border: 3px solid #ddd;
            border-radius: 15px;
            padding: 25px;
            background: #f8f9fa;
        }

        .phase-title {
            background: linear-gradient(45deg, #12c2e9, #c471ed, #f64f59);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }

        .activity-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .activity-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .activity-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
            border-color: #007bff;
        }

        .activity-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
            text-align: center;
        }

        .activity-duration {
            background: #17a2b8;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            display: inline-block;
            margin-bottom: 15px;
        }

        .activity-description {
            color: #6c757d;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .start-activity-btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            width: 100%;
            transition: all 0.3s ease;
        }

        .start-activity-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
        }

        .navigation {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: white;
            padding: 15px;
            border-radius: 50px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .nav-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 0 5px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
        }

        .nav-btn:hover {
            background: #0056b3;
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="timer-display" id="lessonTimer">60:00</div>
    
    <div class="container">
        <div class="header">
            <h1>🎯 60-Minute Interactive Lesson Plan</h1>
            <h2>Module 1 Review: Lifestyles</h2>
            <p>Level: A1-A2 Cambridge English | Focus: City vs. Country • People & Jobs • Present/Simple vs. Progressive • Relative Clauses • Adjectives from Nouns</p>
        </div>

        <!-- PHASE 1: ENGAGE (First 30 minutes) -->
        <div class="phase-container" id="phase1">
            <div class="phase-title">🚀 PHASE 1: ENGAGE (First 30 Minutes)</div>
            
            <div class="activity-grid">
                <div class="activity-card" onclick="startActivity('logic-puzzles')">
                    <div class="activity-title">🧩 Logic Puzzle Warm-up</div>
                    <div class="activity-duration">5-7 minutes</div>
                    <div class="activity-description">
                        Quick puzzles using Module 1 vocabulary (city vs. country, adjectives, jobs) to warm up.
                    </div>
                    <button class="start-activity-btn">Start Puzzles</button>
                </div>

                <div class="activity-card" onclick="startActivity('memory-game')">
                    <div class="activity-title">🎴 Vocabulary Memory Game</div>
                    <div class="activity-duration">10-12 minutes</div>
                    <div class="activity-description">
                        Match pairs from city/country, personality adjectives, and jobs. Correct vs. scrambled forms.
                    </div>
                    <button class="start-activity-btn">Start Memory Game</button>
                </div>

                <div class="activity-card" onclick="startActivity('vocab-quiz')">
                    <div class="activity-title">📝 Vocabulary Description Quiz</div>
                    <div class="activity-duration">10-12 minutes</div>
                    <div class="activity-description">
                        Guess Module 1 items from simple clues: places, adjectives, jobs, and ticket phrases.
                    </div>
                    <button class="start-activity-btn">Start Quiz</button>
                </div>
            </div>
        </div>

        <!-- PHASE 2: STUDY & APPLY (Second 30 minutes) -->
        <div class="phase-container" id="phase2">
            <div class="phase-title">📚 PHASE 2: STUDY & APPLY (Second 30 Minutes)</div>
            
            <div class="activity-grid">
                <div class="activity-card" onclick="startActivity('speed-reading')">
                    <div class="activity-title">⚡ Speed Reading Activity</div>
                    <div class="activity-duration">15 minutes</div>
                    <div class="activity-description">
                        Read diary entries about city vs. country (Meg & Sarah) with comprehension, gap-fills, and ordering.
                    </div>
                    <button class="start-activity-btn">Start Reading</button>
                </div>

                <div class="activity-card" onclick="startActivity('minefield')">
                    <div class="activity-title">💣 Pronunciation Minefield</div>
                    <div class="activity-duration">7-8 minutes</div>
                    <div class="activity-description">
                        Navigate the minefield while practicing key vocabulary pronunciation from the unit.
                    </div>
                    <button class="start-activity-btn">Start Minefield</button>
                </div>

                <div class="activity-card" onclick="startActivity('tic-tac-toe')">
                    <div class="activity-title">⭕ Grammar Tic-Tac-Toe</div>
                    <div class="activity-duration">7-8 minutes</div>
                    <div class="activity-description">
                        Practice simple present vs. present progressive, relative clauses, and adjective formation.
                    </div>
                    <button class="start-activity-btn">Start Game</button>
                </div>
            </div>
        </div>

        <!-- BONUS ACTIVITIES -->
        <div class="phase-container" id="bonus">
            <div class="phase-title">🏆 BONUS ACTIVITIES</div>
            
            <div class="activity-grid">
                <div class="activity-card" onclick="startActivity('jeopardy')">
                    <div class="activity-title">🎯 Jeopardy Game</div>
                    <div class="activity-duration">Flexible</div>
                    <div class="activity-description">
                        4 teams compete with categories: City vs Country, Personality, Jobs, Grammar, Everyday English.
                    </div>
                    <button class="start-activity-btn">Start Jeopardy</button>
                </div>

                <div class="activity-card" onclick="startActivity('family-feud')">
                    <div class="activity-title">👨‍👩‍👧‍👦 Family Feud</div>
                    <div class="activity-duration">Flexible</div>
                    <div class="activity-description">
                        Reveal top module words/phrases: city/country, personality pairs, jobs, and ticket dialog.
                    </div>
                    <button class="start-activity-btn">Start Family Feud</button>
                </div>
            </div>
        </div>
    </div>

    <div class="navigation">
        <button class="nav-btn" onclick="showPhase('phase1')">Phase 1</button>
        <button class="nav-btn" onclick="showPhase('phase2')">Phase 2</button>
        <button class="nav-btn" onclick="showPhase('bonus')">Bonus</button>
        <button class="nav-btn" onclick="resetLesson()">Reset</button>
    </div>

    <script>
        // Timer functionality
        function startTimer(duration, display) {
            let timer = duration, minutes, seconds;
            let countdown = setInterval(function () {
                minutes = parseInt(timer / 60, 10);
                seconds = parseInt(timer % 60, 10);

                minutes = minutes < 10 ? "0" + minutes : minutes;
                seconds = seconds < 10 ? "0" + seconds : seconds;

                display.textContent = minutes + ":" + seconds;

                if (--timer < 0) {
                    clearInterval(countdown);
                    display.textContent = "Time's Up!";
                }
            }, 1000);
        }

        // Start the timer when the page loads
        window.onload = function () {
            let sixtyMinutes = 60 * 60,
                display = document.querySelector('#lessonTimer');
            startTimer(sixtyMinutes, display);
        };

        // Function to start activities
        function startActivity(activityName) {
            // Instead of opening in a new window, redirect to the activity page
            window.location.href = activityName + '.html';
        }

        function showPhase(phaseId) {
            // Hide all phases
            document.querySelectorAll('.phase-container').forEach(phase => {
                phase.style.display = 'none';
            });
            
            // Show selected phase
            document.getElementById(phaseId).style.display = 'block';
        }

        function resetLesson() {
            // Reset the timer
            let sixtyMinutes = 60 * 60,
                display = document.querySelector('#lessonTimer');
            startTimer(sixtyMinutes, display);
            
            // Show all phases
            document.querySelectorAll('.phase-container').forEach(phase => {
                phase.style.display = 'block';
            });
            
            if (confirm('Reset lesson timer and start over?')) {
                startTimer(sixtyMinutes, display);
            }
        }
    </script>
</body>
</html> 