<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vocabulary Memory Game - Module 6&7 Review: Health & Sports</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(45deg, #12c2e9, #c471ed);
            color: white;
            border-radius: 15px;
        }

        .game-controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
        }

        .control-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            background: #0056b3;
            transform: scale(1.05);
        }

        .shuffle-btn {
            background: #ff6b6b;
        }

        .shuffle-btn:hover {
            background: #ff5252;
        }

        .game-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }

        .memory-card {
            aspect-ratio: 1;
            background: #f8f9fa;
            border: 3px solid #dee2e6;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .memory-card:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .memory-card.flipped {
            background: #e3f2fd;
            border-color: #2196f3;
            color: #1976d2;
        }

        .memory-card.matched {
            background: #e8f5e8;
            border-color: #4caf50;
            color: #2e7d32;
            cursor: default;
        }

        .memory-card.wrong {
            background: #ffebee;
            border-color: #f44336;
            color: #c62828;
            animation: shake 0.5s ease-in-out;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .card-label {
            position: absolute;
            top: 5px;
            left: 8px;
            font-size: 10px;
            color: #666;
            font-weight: normal;
        }

        .card-back {
            background: linear-gradient(45deg, #ff9a9e, #fecfef);
            color: white;
            font-size: 24px;
        }

        .stats {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }

        .stat-label {
            font-size: 14px;
            color: #666;
        }

        .success-message {
            text-align: center;
            padding: 20px;
            background: linear-gradient(45deg, #4caf50, #45a049);
            color: white;
            border-radius: 15px;
            margin-top: 20px;
            display: none;
            font-size: 18px;
            font-weight: bold;
        }

        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="lesson_plan_main.html" class="back-btn">← Main Menu</a>
        <div class="header">
            <h1>🎴 Vocabulary Memory Game</h1>
            <p>Match correct spellings with scrambled versions from Module 6&7 vocabulary.</p>
            <p>Health problems, Sports, Transportation, and Adjectives</p>
        </div>

        <div class="game-controls">
            <button class="control-btn shuffle-btn" onclick="shuffleCards()">🔀 Shuffle Cards</button>
            <button class="control-btn" onclick="resetGame()">🔄 New Game</button>
            <button class="control-btn" onclick="window.close()">✅ Finish Game</button>
        </div>

        <div class="stats">
            <div class="stat-item">
                <div class="stat-number" id="movesCount">0</div>
                <div class="stat-label">Moves</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="matchesCount">0</div>
                <div class="stat-label">Matches</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="timeCount">0</div>
                <div class="stat-label">Seconds</div>
            </div>
        </div>

        <div class="game-grid" id="gameGrid">
            <!-- Cards will be generated here -->
        </div>

        <div class="success-message" id="successMessage">
            🎉 Congratulations! You found all the pairs! 
            <br>Your vocabulary skills are excellent!
        </div>
    </div>

    <script>
        // Vocabulary pairs (correct spelling and scrambled version) - Module 6&7 Review
        const vocabularyPairs = [
            // Health problems
            { correct: "stomachache", scrambled: "stomhacaehe" },
            { correct: "headache", scrambled: "heaadche" },
            { correct: "sore throat", scrambled: "sroe thorat" },
            { correct: "fever", scrambled: "fevr e" },

            // Sports
            { correct: "snowboarding", scrambled: "snowbordaing" },
            { correct: "kayaking", scrambled: "kaykaing" },
            { correct: "cycling", scrambled: "cycinlg" },
            { correct: "motocross", scrambled: "motocorss" },

            // Sports adjectives
            { correct: "expensive", scrambled: "expnesive" },
            { correct: "dangerous", scrambled: "dangeorus" },
            { correct: "exciting", scrambled: "excitign" },
            { correct: "challenging", scrambled: "challengnig" },

            // Transportation
            { correct: "gondola", scrambled: "gondloa" },
            { correct: "snowmobile", scrambled: "snowmoblie" },
            { correct: "rickshaw", scrambled: "rickshwa" },
            { correct: "helicopter", scrambled: "helicopetr" }
        ];

        let cards = [];
        let flippedCards = [];
        let matchedPairs = 0;
        let moveCount = 0;
        let gameStartTime = null;
        let gameTimer = null;

        function createCards() {
            cards = [];
            
            // Create pairs of cards
            vocabularyPairs.forEach((pair, index) => {
                cards.push({
                    id: `correct_${index}`,
                    text: pair.correct,
                    type: 'correct',
                    pairId: index,
                    matched: false,
                    flipped: false
                });
                
                cards.push({
                    id: `scrambled_${index}`,
                    text: pair.scrambled,
                    type: 'scrambled',
                    pairId: index,
                    matched: false,
                    flipped: false
                });
            });
            
            shuffleArray(cards);
        }

        function shuffleArray(array) {
            for (let i = array.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [array[i], array[j]] = [array[j], array[i]];
            }
        }

        function shuffleCards() {
            if (matchedPairs > 0) {
                if (!confirm('This will reset your progress. Are you sure?')) {
                    return;
                }
                resetGame();
                return;
            }
            shuffleArray(cards);
            renderGrid();
        }

        function renderGrid() {
            const grid = document.getElementById('gameGrid');
            grid.innerHTML = '';

            cards.forEach((card, index) => {
                const cardElement = document.createElement('div');
                cardElement.className = 'memory-card';
                cardElement.onclick = () => flipCard(index);
                
                // Add grid position labels (A1, A2, B1, etc.)
                const row = String.fromCharCode(65 + Math.floor(index / 4)); // A, B, C, D
                const col = (index % 4) + 1; // 1, 2, 3, 4
                
                if (card.flipped || card.matched) {
                    cardElement.classList.add(card.matched ? 'matched' : 'flipped');
                    cardElement.innerHTML = `
                        <div class="card-label">${row}${col}</div>
                        ${card.text}
                    `;
                } else {
                    cardElement.classList.add('card-back');
                    cardElement.innerHTML = `
                        <div class="card-label">${row}${col}</div>
                        🎴
                    `;
                }
                
                grid.appendChild(cardElement);
            });
        }

        function flipCard(index) {
            if (!gameStartTime) {
                startGameTimer();
            }

            const card = cards[index];
            
            // Don't flip if already flipped, matched, or if 2 cards are already flipped
            if (card.flipped || card.matched || flippedCards.length >= 2) {
                return;
            }

            card.flipped = true;
            flippedCards.push(index);
            renderGrid();

            if (flippedCards.length === 2) {
                moveCount++;
                document.getElementById('movesCount').textContent = moveCount;
                
                setTimeout(() => {
                    checkMatch();
                }, 1000);
            }
        }

        function checkMatch() {
            const [index1, index2] = flippedCards;
            const card1 = cards[index1];
            const card2 = cards[index2];

            if (card1.pairId === card2.pairId) {
                // Match found!
                card1.matched = true;
                card2.matched = true;
                matchedPairs++;
                document.getElementById('matchesCount').textContent = matchedPairs;

                if (matchedPairs === vocabularyPairs.length) {
                    setTimeout(() => {
                        gameComplete();
                    }, 500);
                }
            } else {
                // No match
                card1.flipped = false;
                card2.flipped = false;
                
                // Show wrong animation
                setTimeout(() => {
                    const cardElements = document.querySelectorAll('.memory-card');
                    cardElements[index1].classList.add('wrong');
                    cardElements[index2].classList.add('wrong');
                    
                    setTimeout(() => {
                        cardElements[index1].classList.remove('wrong');
                        cardElements[index2].classList.remove('wrong');
                        renderGrid();
                    }, 500);
                }, 100);
            }

            flippedCards = [];
        }

        function startGameTimer() {
            gameStartTime = Date.now();
            gameTimer = setInterval(() => {
                const elapsed = Math.floor((Date.now() - gameStartTime) / 1000);
                document.getElementById('timeCount').textContent = elapsed;
            }, 1000);
        }

        function gameComplete() {
            clearInterval(gameTimer);
            document.getElementById('successMessage').style.display = 'block';
            
            const finalTime = Math.floor((Date.now() - gameStartTime) / 1000);
            document.getElementById('successMessage').innerHTML = `
                🎉 Congratulations! You found all the pairs!<br>
                Time: ${finalTime} seconds | Moves: ${moveCount}<br>
                Your vocabulary skills are excellent!
            `;
        }

        function resetGame() {
            clearInterval(gameTimer);
            matchedPairs = 0;
            moveCount = 0;
            gameStartTime = null;
            flippedCards = [];
            
            document.getElementById('matchesCount').textContent = '0';
            document.getElementById('movesCount').textContent = '0';
            document.getElementById('timeCount').textContent = '0';
            document.getElementById('successMessage').style.display = 'none';
            
            createCards();
            renderGrid();
        }

        // Initialize game when page loads
        window.onload = function() {
            resetGame();
        };
    </script>
</body>
</html> 