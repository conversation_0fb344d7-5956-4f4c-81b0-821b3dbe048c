<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Jeopardy Game - Module 6&7 Review: Health & Sports</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(45deg, #f093fb, #f5576c);
            color: white;
            border-radius: 15px;
        }

        .scoreboard {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-bottom: 10px;
        }

        .team-score {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            border: 3px solid #dee2e6;
            transition: all 0.3s ease;
        }

        .team-score.active {
            border-color: #28a745;
            background: #d4edda;
            transform: scale(1.05);
        }

        .jeopardy-board {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 10px;
            margin: 20px 0;
        }

        .category-header {
            background: #007bff;
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: bold;
            border-radius: 10px;
            font-size: 14px;
        }

        .question-cell {
            background: #ffc107;
            color: #333;
            padding: 20px;
            text-align: center;
            font-weight: bold;
            font-size: 18px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .question-cell:hover {
            background: #ffb300;
            transform: scale(1.05);
        }

        .question-cell.answered {
            background: #6c757d;
            color: white;
            cursor: not-allowed;
        }

        .question-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 1000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 30px;
            border-radius: 20px;
            max-width: 600px;
            width: 90%;
            text-align: center;
        }

        .question-text {
            font-size: 24px;
            margin-bottom: 30px;
            color: #333;
            font-weight: bold;
        }

        .options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 30px;
        }

        .option {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            padding: 15px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
        }

        .option:hover {
            background: #e9ecef;
            border-color: #007bff;
        }

        .option.correct {
            background: #28a745;
            color: white;
            border-color: #1e7e34;
        }

        .option.incorrect {
            background: #dc3545;
            color: white;
            border-color: #c82333;
        }

        .timer {
            font-size: 48px;
            font-weight: bold;
            color: #dc3545;
            margin: 20px 0;
        }

        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }

        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .back-btn {
            background: #28a745;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            display: inline-block;
            margin-bottom: 20px;
            font-weight: bold;
        }

        .back-btn:hover {
            background: #1e7e34;
            text-decoration: none;
            color: white;
        }

        .controls {
            text-align: center;
            margin: 20px 0;
        }

        .game-over {
            display: none;
            text-align: center;
            padding: 30px;
            background: #d1ecf1;
            border-radius: 15px;
            margin-top: 20px;
        }

        @media (max-width: 768px) {
            .jeopardy-board {
                grid-template-columns: repeat(3, 1fr);
            }
            
            .scoreboard {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .options {
                grid-template-columns: 1fr;
            }
            
            .question-text {
                font-size: 18px;
            }
            
            .category-header {
                font-size: 12px;
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="lesson_plan_main.html" class="back-btn">← Main Menu</a>
        <div class="header">
            <h1>🎯 Jeopardy Game</h1>
            <p>Module 6&7 Review: Health Problems, Sports, Injuries, Transportation, Grammar</p>
            <p><strong>4 Teams • 30 Second Timer • Multiple Choice Questions</strong></p>
        </div>

        <div class="scoreboard" id="scoreboard">
            <!-- Team scores will be generated here -->
        </div>

        <div class="controls">
            <button class="btn" onclick="nextTeam()">Next Team</button>
            <button class="btn" onclick="resetGame()">Reset Game</button>
        </div>

        <div class="jeopardy-board" id="jeopardyBoard">
            <!-- Game board will be generated here -->
        </div>

        <div class="game-over" id="gameOver">
            <!-- Game over message will appear here -->
        </div>
    </div>

    <!-- Question Modal -->
    <div class="question-modal" id="questionModal">
        <div class="modal-content">
            <div class="question-text" id="questionText"></div>
            <div class="timer" id="timer">30</div>
            <div class="options" id="optionsContainer"></div>
            <button class="btn" onclick="closeModal()">Close</button>
        </div>
    </div>

    <script>
        const teams = [
            { name: 'Team 1', score: 0, active: true },
            { name: 'Team 2', score: 0, active: false },
            { name: 'Team 3', score: 0, active: false },
            { name: 'Team 4', score: 0, active: false }
        ];

        const categories = [
            'Health Problems',
            'Sports',
            'Injuries',
            'Transportation',
            'Grammar'
        ];

        const questions = {
            'Health Problems': {
                100: { q: "What do you have when your stomach hurts?", options: ["Headache", "Stomachache", "Sore throat", "Fever"], correct: 1 },
                200: { q: "What medicine do you take for a headache?", options: ["Vitamins", "Aspirin", "Cough syrup", "Bandage"], correct: 1 },
                300: { q: "When someone feels awful, what should they do?", options: ["Ignore it", "See a doctor", "Exercise more", "Eat candy"], correct: 1 },
                400: { q: "Complete the dialogue: 'Are you OK?' 'No, I feel ___'", options: ["Great", "Awful", "Happy", "Excited"], correct: 1 },
                500: { q: "What's the best advice for health problems?", options: ["Take care of your health", "Ignore symptoms", "Never see doctors", "Only rest"], correct: 0 }
            },
            'Sports': {
                100: { q: "Which sport uses a snowboard?", options: ["Skiing", "Snowboarding", "Ice hockey", "Tennis"], correct: 1 },
                200: { q: "Which adjective describes expensive sports?", options: ["Cheap", "Free", "Expensive", "Easy"], correct: 2 },
                300: { q: "What makes a sport fulfilling according to Mike's email?", options: ["Only cost", "Fun and fulfillment", "Popularity", "Equipment"], correct: 1 },
                400: { q: "Which sport does Mike think is most popular?", options: ["Kayaking", "Snowboarding", "Football", "Cycling"], correct: 2 },
                500: { q: "What can make kayaking dangerous?", options: ["It's boring", "Lack of experience", "Too cheap", "Too popular"], correct: 1 }
            },
            'Injuries': {
                100: { q: "What did Steven Bradbury lose when injured?", options: ["Water", "Blood", "Medicine", "Equipment"], correct: 1 },
                200: { q: "How many stitches did Steven need?", options: ["100", "111", "120", "150"], correct: 1 },
                300: { q: "What body part did Steven hit against the wall?", options: ["Leg", "Arm", "Head", "Foot"], correct: 2 },
                400: { q: "What happened to Steven's left foot?", options: ["It was broken", "He lost feeling", "It was cut", "Nothing"], correct: 1 },
                500: { q: "How did Steven win Olympic gold?", options: ["He was fastest", "Others fell down", "He trained most", "He was lucky"], correct: 1 }
            },
            'Transportation': {
                100: { q: "What transportation is used in Venice?", options: ["Rickshaw", "Snowmobile", "Gondola", "Helicopter"], correct: 2 },
                200: { q: "Who rows the boat in Venice?", options: ["Taxi driver", "Gondolier", "Pilot", "Captain"], correct: 1 },
                300: { q: "What transportation is used in Alaska tours?", options: ["Helicopter", "Snowmobile", "Car", "Plane"], correct: 1 },
                400: { q: "How much does the rickshaw tour cost?", options: ["20-40 yuan", "30-50 yuan", "40-60 yuan", "50-70 yuan"], correct: 1 },
                500: { q: "Which word best describes the gondola tour?", options: ["Adventurous", "Romantic", "Dangerous", "Expensive"], correct: 1 }
            },
            'Grammar': {
                100: { q: "Complete: 'I __ a stomachache.' (have)", options: ["have", "has", "having", "had"], correct: 0 },
                200: { q: "Complete: 'You should __ a doctor.' (see)", options: ["seeing", "saw", "see", "sees"], correct: 2 },
                300: { q: "Complete: 'Sports __ be expensive.' (can)", options: ["can", "could", "should", "must"], correct: 0 },
                400: { q: "Complete: 'Steven __ his head.' (hit - past)", options: ["hit", "hits", "hitting", "hitted"], correct: 0 },
                500: { q: "Complete: 'The tour __ one hour.' (last)", options: ["last", "lasts", "lasting", "lasted"], correct: 1 }
            }
        };

        let currentTeamIndex = 0;
        let gameActive = true;
        let timerInterval;
        let timeLeft = 30;

        function initializeGame() {
            createScoreboard();
            createBoard();
        }

        function createScoreboard() {
            const scoreboard = document.getElementById('scoreboard');
            scoreboard.innerHTML = '';
            
            teams.forEach((team, index) => {
                const teamDiv = document.createElement('div');
                teamDiv.className = `team-score ${team.active ? 'active' : ''}`;
                teamDiv.innerHTML = `
                    <h3>${team.name}</h3>
                    <p>$${team.score}</p>
                `;
                scoreboard.appendChild(teamDiv);
            });
        }

        function createBoard() {
            const board = document.getElementById('jeopardyBoard');
            board.innerHTML = '';

            // Create category headers
            categories.forEach(category => {
                const header = document.createElement('div');
                header.className = 'category-header';
                header.textContent = category;
                board.appendChild(header);
            });

            // Create question cells
            const points = [100, 200, 300, 400, 500];
            points.forEach(point => {
                categories.forEach(category => {
                    const cell = document.createElement('div');
                    cell.className = 'question-cell';
                    cell.textContent = point;
                    cell.onclick = () => openQuestion(category, point);
                    cell.setAttribute('data-category', category);
                    cell.setAttribute('data-points', point);
                    board.appendChild(cell);
                });
            });
        }

        function openQuestion(category, points) {
            if (!gameActive) return;
            
            const cell = document.querySelector(`[data-category="${category}"][data-points="${points}"]`);
            if (cell.classList.contains('answered')) return;
            
            const question = questions[category][points];
            
            document.getElementById('questionText').textContent = question.q;
            
            const optionsContainer = document.getElementById('optionsContainer');
            optionsContainer.innerHTML = '';
            
            question.options.forEach((option, index) => {
                const optionDiv = document.createElement('div');
                optionDiv.className = 'option';
                optionDiv.textContent = option;
                optionDiv.onclick = () => selectAnswer(index, question.correct, points, cell);
                optionsContainer.appendChild(optionDiv);
            });
            
            document.getElementById('questionModal').style.display = 'block';
            startTimer();
        }

        function selectAnswer(selected, correct, points, cell) {
            clearInterval(timerInterval);
            
            const options = document.querySelectorAll('.option');
            options.forEach((option, index) => {
                if (index === correct) {
                    option.classList.add('correct');
                } else if (index === selected && index !== correct) {
                    option.classList.add('incorrect');
                }
                option.onclick = null;
            });
            
            if (selected === correct) {
                teams[currentTeamIndex].score += points;
            } else {
                teams[currentTeamIndex].score -= points;
            }
            
            cell.classList.add('answered');
            cell.textContent = '✓';
            
            updateScoreboard();
            
            setTimeout(() => {
                closeModal();
                nextTeam();
            }, 3000);
        }

        function startTimer() {
            timeLeft = 30;
            document.getElementById('timer').textContent = timeLeft;
            
            timerInterval = setInterval(() => {
                timeLeft--;
                document.getElementById('timer').textContent = timeLeft;
                
                if (timeLeft <= 0) {
                    clearInterval(timerInterval);
                    // Time's up - no points awarded
                    setTimeout(() => {
                        closeModal();
                        nextTeam();
                    }, 1000);
                }
            }, 1000);
        }

        function closeModal() {
            document.getElementById('questionModal').style.display = 'none';
            clearInterval(timerInterval);
        }

        function nextTeam() {
            teams[currentTeamIndex].active = false;
            currentTeamIndex = (currentTeamIndex + 1) % teams.length;
            teams[currentTeamIndex].active = true;
            updateScoreboard();
            
            // Check if game is over
            const answeredCells = document.querySelectorAll('.question-cell.answered');
            if (answeredCells.length === 25) {
                endGame();
            }
        }

        function updateScoreboard() {
            createScoreboard();
        }

        function endGame() {
            gameActive = false;
            const winner = teams.reduce((prev, current) => (prev.score > current.score) ? prev : current);
            
            document.getElementById('gameOver').style.display = 'block';
            document.getElementById('gameOver').innerHTML = `
                <h2>🎉 Game Over! 🎉</h2>
                <h3>Winner: ${winner.name} with $${winner.score}!</h3>
                <p>Final Scores:</p>
                ${teams.map(team => `<p>${team.name}: $${team.score}</p>`).join('')}
                <button class="btn" onclick="resetGame()">Play Again</button>
            `;
        }

        function resetGame() {
            teams.forEach((team, index) => {
                team.score = 0;
                team.active = index === 0;
            });
            currentTeamIndex = 0;
            gameActive = true;
            
            document.getElementById('gameOver').style.display = 'none';
            
            const cells = document.querySelectorAll('.question-cell');
            cells.forEach(cell => {
                cell.classList.remove('answered');
                cell.textContent = cell.getAttribute('data-points');
            });
            
            updateScoreboard();
        }

        // Initialize the game when page loads
        window.onload = function() {
            initializeGame();
        };
    </script>
</body>
</html>
