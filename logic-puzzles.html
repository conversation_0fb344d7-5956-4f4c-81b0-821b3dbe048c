<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logic Puzzle Warm-up - Module 1: Lifestyles</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border-radius: 15px;
        }

        .puzzle-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            border-left: 5px solid #007bff;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .puzzle-number {
            background: #007bff;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .puzzle-question {
            font-size: 18px;
            margin-bottom: 15px;
            line-height: 1.6;
        }

        .show-answer-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .show-answer-btn:hover {
            background: #218838;
            transform: scale(1.05);
        }

        .answer {
            display: none;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
            font-weight: bold;
            color: #155724;
        }

        .navigation {
            text-align: center;
            margin-top: 30px;
        }

        .nav-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 12px 25px;
            margin: 0 10px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
        }

        .nav-btn:hover {
            background: #5a6268;
        }

        .progress-bar {
            width: 100%;
            height: 10px;
            background: #e9ecef;
            border-radius: 5px;
            margin-bottom: 20px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            width: 0%;
            transition: width 0.3s ease;
        }

        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="lesson_plan_main.html" class="back-btn">← Main Menu</a>
        <div class="header">
            <h1>Logic Puzzles: City, Country, Jobs & Tickets</h1>
            <p>Solve short puzzles using Module 1 vocabulary and phrases.</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressBar"></div>
            </div>
            <p>Progress: <span id="progressText">0/15</span></p>
        </div>

        <div id="puzzleContainer">
            <!-- Puzzles will be generated here -->
        </div>

        <div class="navigation">
            <button class="nav-btn" onclick="resetPuzzles()">🔄 Reset All</button>
            <button class="nav-btn" onclick="window.close()">✅ Finish Activity</button>
        </div>
    </div>

    <script>
        const puzzles = [
            { question: "The agent says, “Next, please.” What does the customer say when asked “One-way or round-trip?”", answer: "Round-trip, please." },
            { question: "City life often has heavy traffic and crowded streets. Which place is usually quiet with fresh air?", answer: "The country / countryside" },
            { question: "Change noun to adjective: danger → ?", answer: "dangerous" },
            { question: "Who takes photos of wild animals during storms: war reporter, wildlife photographer, or storm chaser?", answer: "Wildlife photographer (storm chaser chases storms; war reporter reports from wars)" },
            { question: "Complete the relative clause: The café _____ we meet is near the station.", answer: "where" },
            { question: "If a commuter buys 2 one-way tickets at $2.00 each, how much is the total?", answer: "$4.00" },
            { question: "Opposites: polite ↔ ?", answer: "impolite" },
            { question: "Present progressive: She ____ (take) the subway now.", answer: "is taking" },
            { question: "Simple present vs. progressive: He usually ____ (get) up at 7:30, but today he ____ (sleep) late.", answer: "gets; is sleeping" },
            { question: "Jobs: Which job needs a lot of courage to photograph tornadoes?", answer: "Storm chaser" },
            { question: "Relative clause: I like people ____ are honest.", answer: "who" },
            { question: "Ticket window phrase: The agent asks, “Where to?” What is a correct place answer?", answer: "To the Museum of Natural History / to Boston (any destination)" },
            { question: "City vocabulary: Name one thing that makes transportation convenient.", answer: "The subway / public transportation" },
            { question: "Form the adjective: fame → ?", answer: "famous" },
            { question: "Feeling word from the text that means ‘missing home’.", answer: "homesick" }
        ];

        let solvedCount = 0;

        function generatePuzzles() {
            const container = document.getElementById('puzzleContainer');
            container.innerHTML = '';

            puzzles.forEach((puzzle, index) => {
                const puzzleCard = document.createElement('div');
                puzzleCard.className = 'puzzle-card';
                puzzleCard.innerHTML = `
                    <div class="puzzle-number">${index + 1}</div>
                    <div class="puzzle-question">${puzzle.question}</div>
                    <button class="show-answer-btn" onclick="showAnswer(${index})" id="btn${index}">
                        🤔 Show Answer
                    </button>
                    <div class="answer" id="answer${index}">
                        💡 ${puzzle.answer}
                    </div>
                `;
                container.appendChild(puzzleCard);
            });
        }

        function showAnswer(index) {
            const answerDiv = document.getElementById(`answer${index}`);
            const button = document.getElementById(`btn${index}`);
            
            if (answerDiv.style.display === 'none' || answerDiv.style.display === '') {
                answerDiv.style.display = 'block';
                button.textContent = '✅ Great job!';
                button.style.background = '#ffc107';
                button.style.color = '#212529';
                
                if (button.textContent === '✅ Great job!') {
                    solvedCount++;
                    updateProgress();
                }
            }
        }

        function updateProgress() {
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');
            const percentage = (solvedCount / puzzles.length) * 100;
            
            progressBar.style.width = percentage + '%';
            progressText.textContent = `${solvedCount}/${puzzles.length}`;
            
            if (solvedCount === puzzles.length) {
                setTimeout(() => {
                    alert('🎉 Amazing! You solved all the puzzles! Your brain is warmed up and ready for more activities!');
                }, 500);
            }
        }

        function resetPuzzles() {
            solvedCount = 0;
            updateProgress();
            generatePuzzles();
        }

        // Initialize puzzles when page loads
        window.onload = function() {
            generatePuzzles();
        };
    </script>
</body>
</html> 