<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fun Word Game! - Module 6&7 Review: Health & Sports</title>
    <style>
        body {
            font-family: 'Comic Sans MS', cursive, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f0f9ff;
            color: #333;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #FFD54F;
            color: #333;
            border-radius: 15px;
        }

        .header h1 {
            font-size: 24px;
            margin: 0;
        }

        .quiz-box {
            background: #fff;
            border: 3px solid #4CAF50;
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
        }

        .question-container {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .question {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #1976d2;
        }

        .options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 20px;
        }

        .option {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-weight: bold;
        }

        .option:hover {
            background: #e9ecef;
            border-color: #4CAF50;
            transform: translateY(-2px);
        }

        .option.selected {
            background: #4CAF50;
            color: white;
            border-color: #45a049;
        }

        .option.correct {
            background: #28a745;
            color: white;
            border-color: #1e7e34;
        }

        .option.incorrect {
            background: #dc3545;
            color: white;
            border-color: #c82333;
        }

        .hint {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 10px;
            margin-top: 15px;
            color: #155724;
            font-weight: bold;
            display: none;
        }

        .controls {
            text-align: center;
            margin-top: 20px;
        }

        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: #45a049;
            transform: translateY(-2px);
        }

        .btn:disabled {
            background: #cccccc;
            cursor: not-allowed;
            transform: none;
        }

        .progress-stars {
            text-align: center;
            font-size: 24px;
            margin-bottom: 20px;
        }

        .score-display {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            margin-top: 20px;
        }

        .final-score {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin-top: 20px;
        }

        .back-btn {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            display: inline-block;
            margin-bottom: 20px;
            font-family: 'Comic Sans MS', cursive, sans-serif;
        }

        .back-btn:hover {
            background: #45a049;
            text-decoration: none;
            color: white;
        }

        @media (max-width: 600px) {
            .options {
                grid-template-columns: 1fr;
            }
            
            .container {
                margin: 10px;
                padding: 15px;
            }
            
            .option {
                padding: 12px;
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="lesson_plan_main.html" class="back-btn">← Back to Games</a>
        
        <div class="header">
            <h1>🎮 Fun Word Game! 🎮</h1>
            <p>Guess the words from Module 6&7: Health Problems, Sports, Injuries, and Transportation.</p>
        </div>

        <div class="quiz-box" id="quizContainer">
            <!-- Quiz content will be generated here -->
        </div>

        <div class="final-score" id="finalScore" style="display: none;">
            <!-- Final score will be shown here -->
        </div>
    </div>

    <script>
        const questions = [
            {
                question: "I hurt your belly and make you feel sick. What health problem am I? 🤢",
                options: ["headache", "stomachache", "sore throat", "fever"],
                correct: 1,
                hint: "Yes! A stomachache hurts your belly."
            },
            {
                question: "You take me when you have a headache. What medicine am I? 💊",
                options: ["vitamins", "aspirin", "cough syrup", "bandage"],
                correct: 1,
                hint: "Correct! Aspirin helps with headaches."
            },
            {
                question: "I'm a winter sport that needs a snowboard. What sport am I? 🏂",
                options: ["skiing", "snowboarding", "ice hockey", "figure skating"],
                correct: 1,
                hint: "Right! Snowboarding uses a snowboard."
            },
            {
                question: "I describe sports that cost a lot of money. What adjective am I? 💰",
                options: ["cheap", "expensive", "free", "boring"],
                correct: 1,
                hint: "Yes! Expensive means costs a lot of money."
            },
            {
                question: "I'm a romantic boat ride in Venice. What transportation am I? 🚤",
                options: ["rickshaw", "snowmobile", "gondola", "helicopter"],
                correct: 2,
                hint: "Perfect! Gondolas are romantic boats in Venice."
            },
            {
                question: "Steven Bradbury lost this red liquid when he was injured. What is it? 🩸",
                options: ["water", "blood", "medicine", "paint"],
                correct: 1,
                hint: "Correct! He lost blood from his injury."
            },
            {
                question: "You get these sewn into your skin after a bad cut. What are they? 🪡",
                options: ["bandages", "stitches", "pills", "casts"],
                correct: 1,
                hint: "Right! Stitches are sewn into cuts."
            },
            {
                question: "This person rows the boat in Venice and entertains tourists. Who am I? 🚣",
                options: ["taxi driver", "gondolier", "pilot", "captain"],
                correct: 1,
                hint: "Yes! A gondolier rows gondolas in Venice."
            }
        ];

        let currentQuestion = 0;
        let score = 0;
        let answered = false;

        function showQuestion() {
            const container = document.getElementById('quizContainer');
            const question = questions[currentQuestion];
            
            const progressStars = Array(questions.length).fill('⭐️').map((star, index) => 
                index < currentQuestion ? '⭐️' : '☆'
            ).join('');
            
            container.innerHTML = `
                <div class="progress-stars">${progressStars}</div>
                <div class="question-container">
                    <div class="question">${question.question}</div>
                    <div class="options">
                        ${question.options.map((option, index) => 
                            `<div class="option" onclick="selectAnswer(${index})">${option}</div>`
                        ).join('')}
                    </div>
                    <div class="hint" id="hint">${question.hint}</div>
                </div>
                <div class="controls">
                    <button class="btn" onclick="nextQuestion()" id="nextBtn" disabled>Next Question</button>
                </div>
                <div class="score-display">Question ${currentQuestion + 1} of ${questions.length} | Score: ${score}/${questions.length}</div>
            `;
            answered = false;
        }

        function selectAnswer(selectedIndex) {
            if (answered) return;
            
            const question = questions[currentQuestion];
            const options = document.querySelectorAll('.option');
            const hint = document.getElementById('hint');
            const nextBtn = document.getElementById('nextBtn');
            
            answered = true;
            
            options.forEach((option, index) => {
                if (index === question.correct) {
                    option.classList.add('correct');
                } else if (index === selectedIndex && index !== question.correct) {
                    option.classList.add('incorrect');
                }
                option.style.pointerEvents = 'none';
            });
            
            if (selectedIndex === question.correct) {
                score++;
            }
            
            hint.style.display = 'block';
            nextBtn.disabled = false;
        }

        function nextQuestion() {
            currentQuestion++;
            if (currentQuestion < questions.length) {
                showQuestion();
            } else {
                showFinalScore();
            }
        }

        function showFinalScore() {
            const container = document.getElementById('quizContainer');
            const finalScore = document.getElementById('finalScore');
            
            const percentage = Math.round((score / questions.length) * 100);
            let message = '';
            let emoji = '';
            
            if (percentage >= 90) {
                message = 'Outstanding! You are a vocabulary master!';
                emoji = '🏆';
            } else if (percentage >= 80) {
                message = 'Excellent work! You know your vocabulary well!';
                emoji = '🌟';
            } else if (percentage >= 70) {
                message = 'Good job! Keep practicing to improve!';
                emoji = '👍';
            } else if (percentage >= 60) {
                message = 'Not bad! Review the vocabulary and try again!';
                emoji = '📚';
            } else {
                message = 'Keep studying! Practice makes perfect!';
                emoji = '💪';
            }
            
            container.style.display = 'none';
            finalScore.style.display = 'block';
            finalScore.innerHTML = `
                <h2>${emoji} Quiz Complete! ${emoji}</h2>
                <h3>${message}</h3>
                <p><strong>Final Score: ${score}/${questions.length} (${percentage}%)</strong></p>
                <button class="btn" onclick="restartQuiz()">🔄 Try Again</button>
                <a href="lesson_plan_main.html" class="btn" style="text-decoration: none; margin-left: 10px;">🏠 Back to Games</a>
            `;
        }

        function restartQuiz() {
            currentQuestion = 0;
            score = 0;
            answered = false;
            document.getElementById('quizContainer').style.display = 'block';
            document.getElementById('finalScore').style.display = 'none';
            showQuestion();
        }

        // Start the quiz when page loads
        window.onload = function() {
            showQuestion();
        };
    </script>
</body>
</html>
