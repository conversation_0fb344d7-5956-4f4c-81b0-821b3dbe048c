<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pronunciation Minefield - Module 6&7: Health & Sports</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4, #45B7D1, #96CEB4);
            background-size: 400% 400%;
            animation: gradientShift 10s ease infinite;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border-radius: 15px;
        }

        .header h1 {
            margin: 0 0 10px 0;
            font-size: 28px;
        }

        .header p {
            margin: 5px 0;
            font-size: 16px;
        }

        .teams-container {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-bottom: 30px;
        }

        .team-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            border: 3px solid #dee2e6;
            transition: all 0.3s ease;
        }

        .team-card.active {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            border-color: #004085;
            transform: scale(1.05);
            box-shadow: 0 10px 20px rgba(0,123,255,0.3);
        }

        .team-card.eliminated {
            background: #6c757d;
            color: white;
            opacity: 0.6;
        }

        .team-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .team-lives {
            display: flex;
            justify-content: center;
            gap: 8px;
            margin-bottom: 10px;
        }

        .life-heart {
            font-size: 24px;
            color: #e74c3c;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .life-heart.lost {
            color: #bdc3c7;
            opacity: 0.5;
        }

        .current-turn {
            text-align: center;
            font-size: 22px;
            font-weight: bold;
            margin: 20px 0;
            padding: 15px;
            background: linear-gradient(45deg, #ffeaa7, #fdcb6e);
            border-radius: 15px;
            color: #2d3436;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .instructions {
            text-align: center;
            background: #e3f2fd;
            border: 2px solid #2196F3;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            color: #1565C0;
            font-weight: bold;
        }

        .minefield-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 8px;
            max-width: 700px;
            margin: 0 auto;
            padding: 20px;
            background: #f0f0f0;
            border-radius: 15px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .mine-cell {
            aspect-ratio: 1;
            background: #ffffff;
            border: 3px solid #cccccc;
            border-radius: 12px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            color: #333333;
            transition: all 0.3s ease;
            position: relative;
            min-height: 80px;
        }

        .mine-cell:hover:not(.revealed) {
            background: #e3f2fd;
            transform: scale(1.05);
            border-color: #2196F3;
            box-shadow: 0 5px 15px rgba(33, 150, 243, 0.3);
        }

        .mine-cell.revealed {
            cursor: default;
            transform: scale(1);
        }

        .mine-cell.safe {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            border-color: #1e8449;
            color: white;
            animation: safeReveal 0.5s ease-in-out;
        }

        .mine-cell.bomb {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            border-color: #922b21;
            color: white;
            animation: bombReveal 0.5s ease-in-out;
        }

        @keyframes safeReveal {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        @keyframes bombReveal {
            0% { transform: scale(1) rotate(0deg); }
            25% { transform: scale(1.1) rotate(-5deg); }
            50% { transform: scale(1.2) rotate(5deg); }
            75% { transform: scale(1.1) rotate(-2deg); }
            100% { transform: scale(1) rotate(0deg); }
        }

        .cell-coordinate {
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 5px;
            background: rgba(0,0,0,0.05);
            color: #333333;
            padding: 2px 6px;
            border-radius: 4px;
        }

        .cell-word {
            font-size: 11px;
            text-align: center;
            line-height: 1.2;
            font-weight: bold;
            color: #000000;
        }

        .bomb-icon {
            font-size: 28px;
            margin-top: 5px;
        }

        .game-controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
        }

        .control-btn {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.4);
        }

        .reset-btn {
            background: linear-gradient(45deg, #6c757d, #545b62);
        }

        .reset-btn:hover {
            box-shadow: 0 5px 15px rgba(108,117,125,0.4);
        }

        .game-over {
            text-align: center;
            padding: 30px;
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border-radius: 15px;
            margin-top: 20px;
            display: none;
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .game-over h2 {
            margin: 0 0 15px 0;
            font-size: 32px;
        }

        .game-over.elimination {
            background: linear-gradient(45deg, #dc3545, #c82333);
        }

        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: linear-gradient(45deg, #6c5ce7, #a29bfe);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .stats {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-bottom: 20px;
            font-size: 16px;
            font-weight: bold;
        }

        .stat-item {
            background: #f8f9fa;
            padding: 10px 20px;
            border-radius: 25px;
            border: 2px solid #dee2e6;
        }
    </style>
</head>
<body>
    <a href="lesson_plan_main.html" class="back-btn">← Back to Lesson</a>
    
    <div class="container">
        <div class="header">
            <h1>💣 Pronunciation Minefield</h1>
            <p><strong>Health Problems • Sports & Adjectives • Transportation</strong></p>
            <p>6×6 Grid • 4 Teams • 3 Lives Each • 12 Hidden Bombs</p>
        </div>

        <div class="instructions">
            <strong>How to play:</strong> Teams call out coordinates (A1–F6), say the word/phrase clearly, then the teacher clicks to reveal.
            Vocabulary themes: health problems, sports & adjectives, transportation, and body parts. Green = Safe, Red = Bomb (lose 1 life).
        </div>

        <div class="stats">
            <div class="stat-item">
                <span id="cellsRevealed">Cells Revealed: 0/36</span>
            </div>
            <div class="stat-item">
                <span id="bombsRemaining">Bombs Remaining: 12</span>
            </div>
        </div>

        <div class="teams-container" id="teamsContainer">
            <!-- Teams will be generated here -->
        </div>

        <div class="current-turn" id="currentTurn">
            🎯 Team 1's Turn - Choose a coordinate!
        </div>

        <div class="minefield-grid" id="minefield">
            <!-- Minefield cells will be generated here -->
        </div>

        <div class="game-controls">
            <button class="control-btn reset-btn" onclick="resetGame()">🔄 New Game</button>
        </div>

        <div class="game-over" id="gameOver">
            <!-- Game over message will appear here -->
        </div>
    </div>

    <script>
        const teams = [
            { name: 'Team 1', lives: 3, active: true, eliminated: false, color: '#e74c3c' },
            { name: 'Team 2', lives: 3, active: false, eliminated: false, color: '#3498db' },
            { name: 'Team 3', lives: 3, active: false, eliminated: false, color: '#2ecc71' },
            { name: 'Team 4', lives: 3, active: false, eliminated: false, color: '#f39c12' }
        ];

        // Module 6&7 vocabulary: health, sports, transportation (content-only change)
        const weekendActivities = [
            'stomachache',
            'headache',
            'sore throat',
            'cold',
            'fever',
            'cough',
            'Are you OK?',
            'I feel awful',
            'What\'s wrong?',
            'You should see a doctor',
            'football',
            'cycling',
            'skiing',
            'kayaking',
            'motocross',
            'snowboarding',
            'dangerous',
            'exciting',
            'challenging',
            'expensive',
            'boring',
            'difficult',
            'break leg',
            'sprain wrist',
            'hit head',
            'cut leg',
            'pull muscle',
            'scrape knee',
            'twist ankle',
            'tram',
            'gondola',
            'yacht',
            'motorbike',
            'snowmobile',
            'riverboat',
            'helicopter',
            'hovercraft',
            'rickshaw'
        ];

        let currentTeamIndex = 0;
        let minefield = [];
        let gameActive = true;
        let cellsRevealed = 0;
        let bombsFound = 0;

        function initializeGame() {
            resetTeams();
            createMinefield();
            updateTeamsDisplay();
            updateCurrentTurn();
            updateStats();
        }

        function resetTeams() {
            teams.forEach(team => {
                team.lives = 3;
                team.eliminated = false;
                team.active = false;
            });
            teams[0].active = true;
            currentTeamIndex = 0;
        }

        function createMinefield() {
            minefield = [];
            cellsRevealed = 0;
            bombsFound = 0;
            
            // Generate random bomb positions (12 bombs out of 36 cells)
            const bombPositions = generateBombPositions(12);
            
            for (let row = 0; row < 6; row++) {
                for (let col = 0; col < 6; col++) {
                    const cellIndex = row * 6 + col;
                    const coordinate = String.fromCharCode(65 + row) + (col + 1);
                    const isBomb = bombPositions.includes(cellIndex);
                    // Cycle through the 8 weekend activities
                    const word = weekendActivities[cellIndex % weekendActivities.length];
                    
                    minefield.push({
                        coordinate: coordinate,
                        word: word,
                        isBomb: isBomb,
                        revealed: false
                    });
                }
            }
            
            renderMinefield();
        }

        function generateBombPositions(numBombs) {
            const positions = [];
            while (positions.length < numBombs) {
                const pos = Math.floor(Math.random() * 36);
                if (!positions.includes(pos)) {
                    positions.push(pos);
                }
            }
            return positions;
        }

        function renderMinefield() {
            const container = document.getElementById('minefield');
            container.innerHTML = '';
            
            minefield.forEach((cell, index) => {
                const cellElement = document.createElement('div');
                cellElement.className = 'mine-cell';
                
                if (!cell.revealed) {
                    cellElement.onclick = () => clickCell(index);
                    cellElement.innerHTML = `
                        <div class="cell-coordinate">${cell.coordinate}</div>
                        <div class="cell-word">${cell.word}</div>
                    `;
                } else {
                    cellElement.classList.add('revealed');
                    if (cell.isBomb) {
                        cellElement.classList.add('bomb');
                        cellElement.innerHTML = `
                            <div class="cell-coordinate">${cell.coordinate}</div>
                            <div class="bomb-icon">💣</div>
                        `;
                    } else {
                        cellElement.classList.add('safe');
                        cellElement.innerHTML = `
                            <div class="cell-coordinate">${cell.coordinate}</div>
                            <div class="cell-word">${cell.word}</div>
                        `;
                    }
                }
                
                container.appendChild(cellElement);
            });
        }

        function clickCell(index) {
            if (!gameActive || minefield[index].revealed) return;
            
            const cell = minefield[index];
            cell.revealed = true;
            cellsRevealed++;
            
            if (cell.isBomb) {
                // Hit a bomb - lose a life
                teams[currentTeamIndex].lives--;
                bombsFound++;
                
                if (teams[currentTeamIndex].lives <= 0) {
                    teams[currentTeamIndex].eliminated = true;
                    teams[currentTeamIndex].active = false;
                }
                
                // Check if game is over
                checkGameEnd();
            }
            
            renderMinefield();
            updateTeamsDisplay();
            updateStats();
            
            if (gameActive) {
                nextTeam();
            }
        }

        function nextTeam() {
            teams[currentTeamIndex].active = false;
            
            // Find next active team
            let attempts = 0;
            do {
                currentTeamIndex = (currentTeamIndex + 1) % 4;
                attempts++;
            } while (teams[currentTeamIndex].eliminated && attempts < 4);
            
            const activeTeams = getActiveTeams();
            if (activeTeams.length > 0) {
                teams[currentTeamIndex].active = true;
                updateCurrentTurn();
                updateTeamsDisplay();
            }
        }

        function getActiveTeams() {
            return teams.filter(team => !team.eliminated);
        }

        function checkGameEnd() {
            const activeTeams = getActiveTeams();
            
            if (activeTeams.length <= 1) {
                gameActive = false;
                showGameOver(activeTeams.length === 1 ? activeTeams[0] : null);
            } else if (cellsRevealed === 36) {
                // All cells revealed
                gameActive = false;
                showGameOver(null, true);
            }
        }

        function showGameOver(winner, allCellsRevealed = false) {
            const gameOverDiv = document.getElementById('gameOver');
            
            if (allCellsRevealed) {
                gameOverDiv.innerHTML = `
                    <h2>🎉 Game Complete!</h2>
                    <h3>All cells revealed!</h3>
                    <p>Great job navigating the minefield!</p>
                    <button class="control-btn" onclick="resetGame()">🎮 Play Again</button>
                `;
            } else if (winner) {
                gameOverDiv.innerHTML = `
                    <h2>🏆 Game Over!</h2>
                    <h3>${winner.name} Wins!</h3>
                    <p>Congratulations! You survived the minefield!</p>
                    <button class="control-btn" onclick="resetGame()">🎮 Play Again</button>
                `;
            } else {
                gameOverDiv.innerHTML = `
                    <h2>💥 Game Over!</h2>
                    <h3>All teams eliminated!</h3>
                    <p>The minefield was too dangerous this time!</p>
                    <button class="control-btn" onclick="resetGame()">🎮 Try Again</button>
                `;
                gameOverDiv.classList.add('elimination');
            }
            
            gameOverDiv.style.display = 'block';
        }

        function updateTeamsDisplay() {
            const container = document.getElementById('teamsContainer');
            container.innerHTML = '';
            
            teams.forEach((team, index) => {
                const teamDiv = document.createElement('div');
                teamDiv.className = 'team-card';
                
                if (team.active && !team.eliminated) {
                    teamDiv.classList.add('active');
                }
                if (team.eliminated) {
                    teamDiv.classList.add('eliminated');
                }
                
                const heartsHtml = Array.from({ length: 4 }, (_, i) => 
                    `<span class="life-heart ${i >= team.lives ? 'lost' : ''}">❤️</span>`
                ).join('');
                
                const statusText = team.eliminated ? 'ELIMINATED' : 
                                  team.active ? 'CURRENT TURN' : 
                                  `${team.lives} LIVES LEFT`;
                
                teamDiv.innerHTML = `
                    <div class="team-name">${team.name}</div>
                    <div class="team-lives">${heartsHtml}</div>
                    <div class="team-status">${statusText}</div>
                `;
                
                container.appendChild(teamDiv);
            });
        }

        function updateCurrentTurn() {
            const activeTeam = teams.find(team => team.active);
            const turnDisplay = document.getElementById('currentTurn');
            
            if (activeTeam) {
                turnDisplay.textContent = `🎯 ${activeTeam.name}'s Turn - Choose a coordinate!`;
            } else {
                turnDisplay.textContent = 'Game Over';
            }
        }

        function updateStats() {
            document.getElementById('cellsRevealed').textContent = `Cells Revealed: ${cellsRevealed}/36`;
            document.getElementById('bombsRemaining').textContent = `Bombs Remaining: ${12 - bombsFound}`;
        }

        function resetGame() {
            if (confirm('Are you sure you want to start a new game?')) {
                gameActive = true;
                document.getElementById('gameOver').style.display = 'none';
                document.getElementById('gameOver').classList.remove('elimination');
                initializeGame();
            }
        }

        // Initialize the game when page loads
        initializeGame();
    </script>
</body>
</html> 
